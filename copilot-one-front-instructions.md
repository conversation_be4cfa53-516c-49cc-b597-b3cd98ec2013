# Tone Of Voice
Keep it by default.

# Memory Bank

The folder `./docs/memory-bank` serves as your **Memory Bank** to keep an active documentation on the project. The goal is to facilitate your agentic approach on new tasks and provide concise but meaningful information to the LLM.

At the beginning of each task, read the **Memory Bank** to gain context on the project and the current task. Use the information in the Memory Bank to plan, execute, and document your tasks.

## Files Structure

### Memory Bank Documentation
- `./docs/memory-bank/ARCHITECTURE.md` - OneFront React boilerplate architecture, TypeScript/Vite stack decisions & best practices
- `./docs/memory-bank/FEATURES.md` - Legal software features: agenda, documents, archive, billing, deadlines, studio management
- `./docs/memory-bank/DEPENDENCIES.md` - React 18, Material-UI, OneFront SDK, FullCalendar, Chart.js, and workflow libraries , vapor design 
- `./docs/memory-bank/README.md` - OneFront React boilerplate documentation and setup guide
- `./docs/memory-bank/CONTRIBUTING.md` - Development contribution guidelines
- `./docs/memory-bank/TROUBLESHOOTING.md` - Common issues and solutions

### Project Structure
- `/app/src/` - Main React TypeScript application source code
- `/app/src/features/` - Feature modules: agenda, archive, anagrafiche, dashboard, documents, fatturazione, mailbox, studio, utility
- `/app/src/components/` - Reusable UI components and layout components
- `/app/src/custom-components/` - Custom business components: modals, forms, data grids, notifications

# Articles
There are no articles for the moment.

# Backlog

- `./docs/backlog/BACKLOG.md` contains the current scope of work, upcoming tasks, and history.
- `./docs/backlog/{tasks|archive}/{BVTL}-human-readable-title.md` contains detailed information about each task, identified by its BVTL (e.g., `BVTL-11111`, taken from the branch name that always contains that information).

### Task BVTLs

The task BVTLs in `./docs/backlog/BACKLOG.md` are used to reference tasks in the Memory Bank. They are formatted as `[BVTL-11111]` where `11111` is the task ID and is an incrementing number taken from the name of the branch on which the user is working on.

**Access the Task's File:** Each task is stored in a file named `{BVTL}-human-readable-title.md` in the `./docs/backlog/{tasks|archive}/` folder. Use the task BVTLs to access the corresponding file.

# IDE Commands

Your first responsibility is to analyze the user's prompt and determine which command to execute based on the provided instructions.

If no command is specified, do your best to infer the user's intent and satisfy their request.

If you find a match, print the command's name in the chat, followed by the action you will take.

## Create New Feature

**Goal:** Create the scaffolding and start implementing the feature that the user asks.

**Triggers:**

- create feature: {feature}

**Examples:**

prompt: `new feature: Anagrafiche `
action: take the task's BVTL, create a new BVTL file, and add it to the backlog.

**Instructions:**

1. Read the BVTL from the branch name of the user
2. Calculate the feature's title, folder name, and description from the content provided in the prompt
   - Use the feature description to create a human-readable title
   - Format the feature folder name as lowercase with hyphens: `{feature-name}`
3. Create the feature structure in `./app/src/features/{feature-name}/` following the agenda pattern:
   
   **Core Files:**
   - `index.tsx` - Route definitions array for OneFront routing
   - `{FeatureName}.tsx` - Main feature component (VaporPage wrapper)
   
   **Folder Structure:**
   - `components/` - Feature-specific UI components
   - `hooks/` - Custom hooks for data fetching and state management
   - `sections/` - Page sections ({FeatureName}Create, {FeatureName}Update, {FeatureName}Details forms)
   - `interfaces/` - TypeScript interfaces and types
   - `utils/` - Utility functions and data transformations
   
   **File Naming Conventions:**
   - Components: PascalCase (`ComponentName.tsx`)
   - Hooks: camelCase with `use` prefix (`useFeatureData.ts`)
   - Types: camelCase with `.interface.ts` suffix
   - Utils: camelCase with `.ts` suffix

4. **Route Integration:**
   - Export route array from `index.tsx` using OneFront pattern:
   ```tsx
   export const featureName = () => [
     {
       target: "$ONE_LAYOUT_ROUTE",
       handler: {
         exact: true,
         path: "/feature-path",
         element: <FeatureComponent />,
       },
     },
   ];
   ```
   
5. **Main App Integration:**
   - Import the feature in `app/src/index.tsx`
   - Add to `sectionMap` object for feature discovery
   - Ensure feature appears in environment variable `VITE_SECTIONS_TO_SHOW`

6. **Component Structure:**
   - Use `VaporPage` as root wrapper for consistent layout
   - Implement `CustomDataGrid` for data tables
   - Use custom components from `/app/src/custom-components/`
   - Follow Vapor design system patterns
   - Prioritize using components from `/app/src/custom-components/` if they exist
   - for icons use FontAwesome

7. **State Management:**
   - Custom hooks for API calls using OneFront SDK
   - Context providers for complex shared state
   - Form management with `react-hook-form` and `yup` validation

**NOTE:** Follow the existing patterns in `/app/src/features/agenda/` for consistency. Each feature should be self-contained with clear separation of concerns.

```markdown
# Title of the BVTL [BVTL-12345]

[[Brief description of the task]]
