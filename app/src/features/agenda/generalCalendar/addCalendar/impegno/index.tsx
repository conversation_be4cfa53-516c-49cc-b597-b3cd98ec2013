import { useState } from "react";
import { <PERSON>ap<PERSON><PERSON><PERSON>, Toolbar } from "@vapor/react-custom";
import { Ta<PERSON>, Tab } from "@vapor/react-extended";
import { But<PERSON>, Stack, Box, Typography } from "@vapor/react-material";
import PageTitle from "../../../../../custom-components/PageTitle";
import InstrumentButtonMenu from "./helpers/instrumentButtonMenu";
import ButtonTimesheetMenu from "./helpers/buttonTimesheetMenu";
import { a11yProps, CustomTabPanel } from "../../../../../helpers/customTabPanel";
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import SchedaImpegno from "./sections/schedaImpegno";
import Instruments from "./sections/instruments";
import Documenti from "./sections/documenti";
import Anagrafiche from "./sections/anagrafiche";
import <PERSON><PERSON><PERSON><PERSON>iuntivi from "./sections/datiAggiuntivi";
import Modelli from "./sections/modelli";
import ImpegniCollegati from "./sections/impegniCollegati";
import useSearchPractica from "./hooks/useSearchPractica";
import useSearchImpegno from "./hooks/useSearchImpegno";
import useSaveDeadlines from "./hooks/useSaveDeadlines";
import useAnagrafiche from "./hooks/useAnagrafiche";
import useCreateFromDeadline from "./hooks/useCreateFromDeadline";
import useSessionStorageState from "./hooks/useSessionStorageState";
import ButtonDelete from "./helpers/ButtonDelete";
import Performance from "./sections/performance/performance";
import useSavePerformance from "./hooks/useSavePerformance";
import { saveToRfc5545 } from "./sections/performance/addPerformance/helpers/saveToRcf5545";
import Spinner from "../../../../../custom-components/Spinner";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import usePerformance from "./hooks/usePerformance";
import usePrintImpegno from "./hooks/usePrintImpegno";
import SpinnerButton from "../../../../../custom-components/SpinnerButton";
import { useCalendarData } from "./context/CalendarDataContext";
import useFetchDocuments from "./hooks/useFetchDocuments";
import useFetchDatiAggiuntivi from "./hooks/useFetchDatiAggiuntivi";

interface LocationState {
    isFromToDoList?: boolean;
    isFromArchive?: boolean;
    isFromDeadlinesList?: boolean;
    fileUniqueid?: string;
    returnUrl?: string;
}

export default function ImpegnoIndex() {
    const navigate = useNavigate();
    const location = useLocation();
    const [searchParams] = useSearchParams();
    const state = location.state as LocationState;
    const { isFromToDoList, isFromArchive, isFromDeadlinesList } = state || {};
    // Get fileUniqueid from URL query parameters first, then from state
    const fileUniqueid = searchParams.get("fileUniqueid") || state?.fileUniqueid || "";
    // Check if the current URL path or referrer contains archivedeadlines/deadlines
    const isFromArchiveDeadlines = location.pathname.includes("archivedeadlines/deadlines") || searchParams.get("from") === "archivedeadlines";
    const navigateBackUrl = isFromToDoList || isFromDeadlinesList ? state?.returnUrl || "/deadlines/deadlines" : isFromArchive ? `/archivedeadlines/deadlines` : "/calendar/calendar";
    const { t } = useTranslation();
    const [showPerformanceForm, setShowPerformanceForm] = useState({
        list: false,
        tariff: false,
        expense: false,
        timesheet: false
    });
    const [showModal, setShowModal] = useState<any>({
        open: false,
        title: "",
        confirmText: "",
        deleteFunc: () => ""
    });
    const [tabValue, setTabValue] = useState<number>(0);
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const {
        data, //its needed on index
        fetchUserGroupById,
        loggedUserName,
        fetchCalendarData,
        deadlineSaveParams,
        setDeadlineSaveParams,
        selectedPraticaName,
        setSelectedPraticaName,
        isUserEditing,
        recurrenceId,
        loading //needed on index
    } = useCalendarData();
    const { practicaSearchResult, handlePracticaSearch, setPracticaSearchResult, searchPracticaLoading, setIsArchiveChecked } = useSearchPractica();
    const { impegnoSearchResult, handleImpegnoSearch, searchImpegnoLoading } = useSearchImpegno();
    const { saveDeadline, requiredFields, setRequiredFields } = useSaveDeadlines();
    const {
        insertAnagrafiche,
        insertAnagraficheParams,
        setInsertAnagraficheParams,
        anagraficheQuery,
        setAnagraficheQuery,
        anagraficheListLoading,
        handleAnagraficheSearch,
        anagraficheResult,
        searchAnagraficheLoading,
        selectedAnagrafica,
        setSelectedAnagrafica,
        handleAddAnagraficheOnCreate,
        handleAddAnagraficheOnUpdate,
        listAnagrafiche
    } = useAnagrafiche();
    const { createFromDeadline } = useCreateFromDeadline();
    const { clearSessionStorage } = useSessionStorageState();
    const { fetchPrestazioniData, listPerformance, performanceQuery, setPerformanceQuery, loadingPerformance } = usePerformance();
    const { documentsQuery, setDocumentsQuery, listDocuments, loadingDocuments, LINK_DOCUMENT } = useFetchDocuments();
    const { fetchDatiAggiuntivi, connect, selectedItem, setSelectedItem, localParentItemId, setLocalParentItemId, toggleItemConnection, loadingDatiAggiuntivi } = useFetchDatiAggiuntivi();

    const { handleSavePerformance, savePerformanceParams, setSavePerformanceParams } = useSavePerformance(showPerformanceForm);

    const { handlePrintRequest } = usePrintImpegno();

    const [showInstrumentsMenu, setShowInstrumentsMenu] = useState<boolean>(false);

    const handleTabsChange = (_: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const [showModalOverlapping, setShowModalOverlapping] = useState<boolean>(false);
    const [confirmText, setConfirmText] = useState<any>([]);

    const handleSaveAction = async (controlOverlapping: boolean = true) => {
        try {
            setSaveLoading(true);
            if (fileUniqueid && !deadlineSaveParams.deadlineFileUniqueid) {
                setDeadlineSaveParams((prev) => ({
                    ...prev,
                    deadlineFileUniqueid: fileUniqueid
                }));
            }
            const result = await saveDeadline(
                fileUniqueid
                    ? {
                          ...deadlineSaveParams,
                          deadlineFileUniqueid: fileUniqueid
                      }
                    : deadlineSaveParams,
                false,
                controlOverlapping
            );

            if (result && result.overlappingDeadlines) {
                const confirmTextArray = [...result.data, "", "Vuoi continuare comunque?"];
                setConfirmText(confirmTextArray);
                setShowModalOverlapping(true);
                return;
            }
            const idScadenzario: any = result;

            if (idScadenzario) {
                if (listAnagrafiche.rows.length > 0) {
                    await insertAnagrafiche(idScadenzario);
                }
                clearSessionStorage();

                // Add the ID as a query parameter when navigating back to the impegni section
                if (isFromArchive) {
                    navigate(`/archivedeadlines/deadlines?fileUniqueid=${fileUniqueid}&id=${idScadenzario}`);
                } else {
                    navigate(navigateBackUrl);
                }
            }
        } catch (error: any) {
            console.error("Error during save action:", error);
        } finally {
            setSaveLoading(false);
        }
    };

    const handleCreateFromDeadline = async (connect: boolean) => {
        try {
            if (fileUniqueid && !deadlineSaveParams.deadlineFileUniqueid) {
                setDeadlineSaveParams((prev) => ({
                    ...prev,
                    deadlineFileUniqueid: fileUniqueid
                }));
            }
            const deadlineUniqueid = await saveDeadline(
                fileUniqueid
                    ? {
                          ...deadlineSaveParams,
                          deadlineFileUniqueid: fileUniqueid
                      }
                    : deadlineSaveParams,
                true
            );
            if (deadlineUniqueid) {
                const finalId = deadlineUniqueid === true ? deadlineSaveParams.deadlineUniqueid : deadlineUniqueid;

                await createFromDeadline(finalId, connect);
                clearSessionStorage();

                // Add the ID as a query parameter when navigating back to the impegni section
                if (isFromArchive) {
                    navigate(`/archivedeadlines/deadlines?fileUniqueid=${fileUniqueid}&id=${finalId}`);
                } else {
                    navigate(navigateBackUrl);
                }
            }
        } catch (error: any) {
            console.error("Error during save action", error);
        }
    };

    const handleModalConfirm = () => {
        handleSaveAction(false);
    };

    const handleDecline = () => {
        setShowModalOverlapping(false);
    };

    const handleConfirmInstruments = (deadlineParams: any) => {
        const rfcData = saveToRfc5545(deadlineParams);
        if (rfcData) {
            setDeadlineSaveParams((prev: any) => ({
                ...prev,
                ...rfcData
            }));
            setShowInstrumentsMenu(false);
        }
    };

    const handleCancelInstruments = () => {
        setShowInstrumentsMenu(false);
    };

    const closeForm = () => {
        setShowPerformanceForm({
            list: false,
            tariff: false,
            expense: false,
            timesheet: false
        });
    };

    const handlePerformanceConfirm = async (deadlineSaveParams: any) => {
        await handleSavePerformance(deadlineSaveParams);
        sessionStorage.setItem("deleteActivity", "true");
        fetchPrestazioniData();
        closeForm();
    };

    return (
        <>
            {loading ? (
                <Spinner />
            ) : (
                <VaporPage
                    contentToolbar={
                        <Toolbar
                            contentRight={
                                <Stack direction="row" gap={1}>
                                    {showInstrumentsMenu ? (
                                        <>
                                            <Button variant="outlined" onClick={handleCancelInstruments}>
                                                {t("Annulla")}
                                            </Button>
                                            <Button variant="contained" onClick={() => handleConfirmInstruments(deadlineSaveParams)}>
                                                {t("Conferma")}
                                            </Button>
                                        </>
                                    ) : (
                                        <>
                                            {Object.values(showPerformanceForm).some(Boolean) && (
                                                <>
                                                    <Button variant="outlined" onClick={closeForm}>
                                                        {t("Annulla")}
                                                    </Button>
                                                    <Button variant="contained" onClick={() => handlePerformanceConfirm(savePerformanceParams)}>
                                                        {t("Conferma")}
                                                    </Button>
                                                </>
                                            )}

                                            {!Object.values(showPerformanceForm).some(Boolean) && (
                                                <>
                                                    <Button
                                                        onClick={
                                                            showInstrumentsMenu
                                                                ? () => setShowInstrumentsMenu(false)
                                                                : () => {
                                                                      navigate(navigateBackUrl);
                                                                      clearSessionStorage();
                                                                  }
                                                        }
                                                        variant="outlined"
                                                    >
                                                        {t("Annulla")}
                                                    </Button>

                                                    {/* Show InstrumentButtonMenu only when not in instruments menu */}
                                                    {!showInstrumentsMenu && (
                                                        <InstrumentButtonMenu
                                                            onClickFunc={() => setShowInstrumentsMenu(true)}
                                                            isUserEditing={isUserEditing}
                                                            recurrenceId={deadlineSaveParams?.recurrenceId || ""}
                                                            deadlineFileUniqueid={data?.impegnoData?.deadlineFileUniqueid}
                                                            deadlineUniqueid={deadlineSaveParams.deadlineUniqueid}
                                                            handlePrintRequest={handlePrintRequest}
                                                        />
                                                    )}
                                                    {isUserEditing && (
                                                        <ButtonDelete
                                                            navigateBackUrl={navigateBackUrl}
                                                            showModal={showModal}
                                                            setShowModal={setShowModal}
                                                            data={data?.impegnoData}
                                                            showDropdown={typeof recurrenceId === "string"}
                                                        />
                                                    )}

                                                    {(deadlineSaveParams.deadlineNonevadere || deadlineSaveParams.deadlineEvasa) && (
                                                        <ButtonTimesheetMenu handleCreateFromDeadline={handleCreateFromDeadline} t={t} />
                                                    )}

                                                    <SpinnerButton variant="contained" onClick={() => handleSaveAction()} label={t("Conferma")} isLoading={saveLoading} />
                                                </>
                                            )}
                                        </>
                                    )}
                                </Stack>
                            }
                        />
                    }
                >
                    <PageTitle
                        title={t("Impegno")}
                        pathToPrevPage={isFromArchive ? `/archivedeadlines/deadlines?fileUniqueid=${fileUniqueid}` : navigateBackUrl}
                        handleBackNavigation={clearSessionStorage}
                    />

                    <VaporPage.Section>
                        <Tabs onChange={handleTabsChange} value={tabValue} size="extraSmall" variant="standard">
                            <Tab label={t("Scheda impegno")} {...a11yProps(0)} />
                            {isUserEditing && (
                                <Tab label={t("Prestazione")} {...a11yProps(1)} /> // wanna add this tab but hide when the condition is not completed
                            )}
                            {isUserEditing && <Tab label={t("Documenti")} {...a11yProps(2)} />}
                            <Tab label={t("Anagrafiche")} {...a11yProps(isUserEditing ? 3 : 2)} />
                            <Tab label={t("Dati aggiuntivi")} {...a11yProps(isUserEditing ? 4 : 3)} />
                            <Tab label={t("Modelli")} {...a11yProps(isUserEditing ? 5 : 4)} />
                            <Tab label={t("Impegni collegati")} {...a11yProps(isUserEditing ? 6 : 5)} />
                        </Tabs>
                    </VaporPage.Section>

                    <Box>
                        <CustomTabPanel value={tabValue} index={0}>
                            {showInstrumentsMenu ? (
                                <Instruments deadlineSaveParams={deadlineSaveParams} setDeadlineSaveParams={setDeadlineSaveParams} />
                            ) : (
                                <SchedaImpegno
                                    data={data}
                                    practicaSearchResult={practicaSearchResult}
                                    setPracticaSearchResult={setPracticaSearchResult}
                                    handlePracticaSearch={handlePracticaSearch}
                                    searchPracticaLoading={searchPracticaLoading}
                                    impegnoSearchResult={impegnoSearchResult}
                                    handleImpegnoSearch={handleImpegnoSearch}
                                    searchImpegnoLoading={searchImpegnoLoading}
                                    deadlineSaveParams={deadlineSaveParams}
                                    setDeadlineSaveParams={setDeadlineSaveParams}
                                    fetchUserGroupById={fetchUserGroupById}
                                    loggedUserName={loggedUserName}
                                    fetchCalendarData={fetchCalendarData}
                                    setIsArchiveChecked={setIsArchiveChecked}
                                    requiredFields={requiredFields}
                                    setRequiredFields={setRequiredFields}
                                    selectedPraticaName={selectedPraticaName}
                                    setSelectedPraticaName={setSelectedPraticaName}
                                    hideFileSelect={(isFromArchive || isFromArchiveDeadlines) && !!fileUniqueid}
                                />
                            )}
                        </CustomTabPanel>
                        {isUserEditing && (
                            <CustomTabPanel value={tabValue} index={1}>
                                <Performance
                                    showPerformanceForm={showPerformanceForm}
                                    setShowPerformanceForm={setShowPerformanceForm}
                                    savePerformanceParams={savePerformanceParams}
                                    setSavePerformanceParams={setSavePerformanceParams}
                                    closeForm={closeForm}
                                    listPerformance={listPerformance}
                                    performanceQuery={performanceQuery}
                                    setPerformanceQuery={setPerformanceQuery}
                                    loading={loadingPerformance}
                                />
                            </CustomTabPanel>
                        )}
                        {isUserEditing && (
                            <CustomTabPanel value={tabValue} index={2}>
                                <Documenti
                                    documentsQuery={documentsQuery}
                                    setDocumentsQuery={setDocumentsQuery}
                                    listDocuments={listDocuments}
                                    loadingDocuments={loadingDocuments}
                                    LINK_DOCUMENT={LINK_DOCUMENT}
                                />
                            </CustomTabPanel>
                        )}
                        <CustomTabPanel value={tabValue} index={isUserEditing ? 3 : 1}>
                            <Anagrafiche
                                insertAnagraficheParams={insertAnagraficheParams}
                                setInsertAnagraficheParams={setInsertAnagraficheParams}
                                anagraficheQuery={anagraficheQuery}
                                setAnagraficheQuery={setAnagraficheQuery}
                                loading={anagraficheListLoading}
                                handleAnagraficheSearch={handleAnagraficheSearch}
                                anagraficheResult={anagraficheResult}
                                searchAnagraficheLoading={searchAnagraficheLoading}
                                selectedAnagrafica={selectedAnagrafica}
                                setSelectedAnagrafica={setSelectedAnagrafica}
                                handleAddAnagraficheOnCreate={handleAddAnagraficheOnCreate}
                                listAnagrafiche={listAnagrafiche}
                                isUserEditing={isUserEditing}
                                handleAddAnagraficheOnUpdate={handleAddAnagraficheOnUpdate}
                            />
                        </CustomTabPanel>
                        <CustomTabPanel value={tabValue} index={isUserEditing ? 4 : 2}>
                            <DatiAggiuntivi
                                fetchDatiAggiuntivi={fetchDatiAggiuntivi}
                                connect={connect}
                                selectedItem={selectedItem}
                                setSelectedItem={setSelectedItem}
                                localParentItemId={localParentItemId}
                                setLocalParentItemId={setLocalParentItemId}
                                toggleItemConnection={toggleItemConnection}
                                loadingDatiAggiuntivi={loadingDatiAggiuntivi}
                            />
                        </CustomTabPanel>
                        <CustomTabPanel value={tabValue} index={isUserEditing ? 5 : 3}>
                            <Modelli />
                        </CustomTabPanel>
                        <CustomTabPanel value={tabValue} index={isUserEditing ? 6 : 4}>
                            <ImpegniCollegati />
                        </CustomTabPanel>
                    </Box>

                    <ConfirmModal
                        open={showModal.open}
                        handleDecline={() => setShowModal({ ...showModal, open: false })}
                        handleAgree={showModal.deleteFunc}
                        decline={t("Annulla")}
                        agree={t("Conferma")}
                        confirmText={t(showModal.confirmText)}
                        title={t(showModal.title)}
                        colorConfirmButton="error"
                    />
                    <ConfirmModal
                        open={showModalOverlapping}
                        handleDecline={handleDecline}
                        handleAgree={handleModalConfirm}
                        decline={t("Annulla")}
                        agree={t("Conferma")}
                        title={t("Conflitti Attività:")}
                    >
                        {confirmText.map((txt: string) => (
                            <Typography>{t(txt)}</Typography>
                        ))}
                    </ConfirmModal>
                </VaporPage>
            )}
        </>
    );
}
