// import { useState } from "react";
import {
    SearchBar, Box,
    Button,
    Checkbox,
    FormControlLabel,
    FormControl,
    Select,
    MenuItem,
    Grid,
    Typography,
    Divider
} from "@vapor/v3-components";
import {
    // calendarCommitmentsList,
    calendarEvasaList,
    // calendarNonevadereList,
} from "./helpers/selectFilterData";
// import SearchAuthority from "./hooks/useSearchAuthority";
import { ICalendarFilterProps } from "./typings/generalCalendar.interface";
// import { useNavigate } from "react-router-dom";
import { gettingCalendarViewName } from "./helpers/gettingCalendarViewName";
import { updateCalendar } from "./helpers/calendarHelper";
import moment from "moment";
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import { SelectMultiple } from "../../../custom-components/SelectMultiple";


export default function Filters(props: ICalendarFilterProps) {
    const {
        query,
        setQuery,
        DEFAULT_QUERY,
        calendarData,
        t,
        calendarRef,
        setMonthTitle,
        setLeftPanelOpen,
    } = props;

    const clearAll = () => {
        try {
            const calendarApi: any = calendarRef.current?.getApi();

            if (!calendarApi || !calendarApi.currentData || !calendarApi.currentDataManager) {
                console.warn('Calendar API not properly initialized for clearAll');
                const fallbackQuery = {
                    ...(DEFAULT_QUERY as any),
                    calendarPersons: [],
                    start: moment().startOf('month').unix(),
                    end: moment().endOf('month').unix(),
                    date: moment().unix(),
                };
                setQuery(fallbackQuery);
                return;
            }

            const calendarViewName = gettingCalendarViewName(
                calendarApi.currentData.currentViewType
            );

            const activeRange = calendarApi.currentDataManager.state?.dateProfile?.activeRange;
            const currentDate = calendarApi.getDate();

            if (!activeRange || !activeRange.start || !activeRange.end || !currentDate) {
                console.warn('Calendar date range not properly initialized');
                const now = moment();
                const fallbackQuery = {
                    ...(DEFAULT_QUERY as any),
                    start: now.startOf('month').unix(),
                    end: now.endOf('month').unix(),
                    date: now.unix(),
                    viewName: calendarViewName,
                    calendarPersons: [],
                };
                setQuery(fallbackQuery);
                return;
            }

            const newStart = activeRange.start.getTime() / 1000;
            const newEnd = activeRange.end.getTime() / 1000;
            const newDate = currentDate.getTime() / 1000;

            // Clear localStorage filters
            localStorage.removeItem("calendarCurrentDate");
            localStorage.removeItem("agendaQuery");
            localStorage.setItem("cleared", "true");

            const updatedQuery: any = {
                ...DEFAULT_QUERY, //this is not updated to query
                start: newStart,
                end: newEnd,
                date: newDate,
                viewName: calendarViewName,
                calendarPersons: [], // Maintain users when clearing filters
                calendarDeadlineTypeValues: [],
                calendarDeadlineCategoryValues: [],
                calendarEvasaValues: [],
                calendarPersonsValues: [],
            };

            setQuery(updatedQuery);

            // Update the calendar view with the cleared filters
            updateCalendar(
                calendarApi,
                updatedQuery,
                setQuery,
                setMonthTitle,
                t,
                calendarViewName
            );
        } catch (error) {
            console.error('Error in clearAll:', error);
            setQuery(DEFAULT_QUERY as any);
        }
    };

    const onChangeInputs = (e: any) => {
        const { name, value } = e.target;
        setQuery({ ...query, [name]: value });
    };

    const onChangeCheckboxes = (e: any) => {
        const { name, checked } = e.target;
        const calendarAPI = calendarRef?.current?.getApi();
        if (name === 'calendarWeekends' && calendarAPI) {
            // If currently in work week view, switch to regular week view when showing weekends
            if (checked && calendarAPI.view.type === 'timeGridWeek' && !calendarAPI.getOption('weekends')) {
                calendarAPI.setOption('weekends', true);
            }
        }
        setQuery({ ...query, [name]: checked });
    };

    // const convertStartDate = (dateString: string) =>
    //     dateString.split("/").reverse().join("/");
    // const convertEndDate = (dateString: string) =>
    //     dateString.split("-").reverse().join("/");

    // const handleNavigateToCommitments = () => {
    //     const startDate = convertStartDate(calendarData.startButtonDate);
    //     const endDate = convertEndDate(calendarData.endButtonDate);
    //     const navigateQuery: string = `/legacy/deadlines/deadlines?oldDeadlines=${true}&startDate=${startDate}&endDate=${endDate}&intestatario=${calendarData.calendarCommitments
    //         }`;
    //     navigate(navigateQuery);
    // };

    const handleChangeMultiSelect = (
        name: any,
        newValue: any
    ) => {
        if (name === "calendarDeadlineType" || name === "calendarDeadlineCategory" || name === "calendarPersons") {
            setQuery({ ...query, [name]: newValue.map((item: any) => item.id) });
        } else if (name === "calendarEvasa") {
            setQuery({ ...query, [name]: newValue.map((item: any) => item.value) });
        }
    };

    const isSoloUdienzeNotSelected =
        query.calendarCommitments !== "hearingsOnly";

    const handleClose = () => {
        setLeftPanelOpen(false);
    };

    console.log(query);

    return (
        <Box
            component="form"
            sx={{
                width: "100%",
                background: '#fff',
                maxHeight: '80vh',
                overflowY: 'auto',
            }}
        >
            <Grid container spacing={2} direction="column">
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography sx={{ fontSize: '22px', fontWeight: 'bold' }}>
                        {t("Filtri")}
                    </Typography>
                    <ClearRoundedIcon onClick={handleClose} fontSize="medium" sx={{ color: '#008fd6' }} />
                </Grid>
                <Grid item xs={12}>
                    <Divider />
                </Grid>
                <Grid item md={10}>
                    <SearchBar
                        variant="icon"
                        buttonVariant="primary"
                        size="medium"
                        placeholder={t("Cerca")}
                        sx={{ width: '100%' }}
                        name="searchbar1f"
                        onChange={onChangeInputs}
                    />
                </Grid>
                <Grid item md={10}>
                    <SelectMultiple
                        style={{ width: "100%" }}
                        width="100%"
                        name="calendarDeadlineType"
                        label={t("Tipologia")}
                        options={calendarData.deadlineTypes || []}
                        target="nome"
                        keyValue="id"
                        selectedValues={query.calendarDeadlineTypeValues}
                        placeholder={t("Seleziona tipologia")}
                        onChange={handleChangeMultiSelect} />
                </Grid>
                {isSoloUdienzeNotSelected && (
                    <>
                        <Grid item md={10}>
                            <SelectMultiple
                                style={{ width: "100%" }}
                                width="100%"
                                name="calendarDeadlineCategory"
                                label={t("Categoria")}
                                options={calendarData.deadlineCategories || []}
                                target="nome"
                                keyValue="id"
                                selectedValues={query.calendarDeadlineCategoryValues}
                                placeholder={t("Seleziona categoria")}
                                onChange={handleChangeMultiSelect} />
                        </Grid>
                    </>
                )}
                <Grid item md={10}>
                    <SelectMultiple
                        style={{ width: "100%" }}
                        width="100%"
                        name="calendarEvasa"
                        label={t("Mostra")}
                        options={calendarEvasaList || []}
                        target="name"
                        keyValue="value"
                        selectedValues={query.calendarEvasaValues}
                        placeholder={t("Seleziona stato")}
                        onChange={handleChangeMultiSelect} />
                </Grid>
                <FormControlLabel
                    control={
                        <Checkbox
                            name="important"
                            checked={query?.important || false}
                            onChange={onChangeCheckboxes}
                            size="small"
                        />
                    }
                    label={t("Importati")}
                    sx={{ ml: 2, mt: 2 }}
                />
                <FormControlLabel
                    control={
                        <Checkbox
                            name="private"
                            checked={query?.private || false}
                            onChange={onChangeCheckboxes}
                            size="small"
                            sx={{ ml: 1 }}
                        />
                    }
                    label={t("Privati")}
                    sx={{ ml: 2, mt: 1 }}
                />
                <Grid item md={10}>
                    <SearchBar
                        variant="icon"
                        buttonVariant="primary"
                        size="medium"
                        sx={{ width: '100%' }}
                        placeholder={t("Cerca pratica")}
                        id="pratica"
                        value={query.pratica}
                        onChange={onChangeInputs}
                        name="pratica"
                    />
                </Grid>
                <Divider sx={{ mt: 2, mb: 1 }} />
                <Grid item md={12}>
                    <FormControl fullWidth>
                        <Select
                            id="calendarGroup"
                            name="calendarGroup"
                            value={query.calendarGroup}
                            onChange={onChangeInputs}
                        >
                            <MenuItem key="-1" value="-1">
                                {t("Tutti i gruppi")}
                            </MenuItem>
                            {(calendarData.groups || []).map(
                                (group: any, index: number) => (
                                    <MenuItem key={index} value={group.id}>
                                        {group.name}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>
                </Grid>
                <FormControlLabel
                    control={
                        <Checkbox
                            name="onlyGroupEvents"
                            checked={query?.onlyGroupEvents || false}
                            onChange={onChangeCheckboxes}
                            size="small"
                            sx={{ ml: 1, mt: 0 }}
                        />
                    }
                    label={t("Solo eventi di gruppo")}
                    sx={{ ml: 2, mt: 2 }}
                />
                {/* Intestatari */}
                {isSoloUdienzeNotSelected && (
                    <Grid item md={10}>
                        <SelectMultiple
                            style={{ width: "100%" }}
                            width="100%"
                            name="calendarPerson"
                            options={calendarData.users || []}
                            target="nomeutente"
                            keyValue="id"
                            selectedValues={query.calendarPersonsValues}
                            placeholder={t("Seleziona gli intestatari")}
                            onChange={handleChangeMultiSelect} />
                    </Grid>
                )}
                {/* Partecipanti */}
                <Divider sx={{ mt: 2, mb: 1 }} />
                {query.calendarCommitments !== "deadlinesOnly" && (
                    <Grid item md={10}>
                        <FormControl fullWidth>
                            <Select
                                id="authoritySearchId"
                                name="authoritySearchId"
                                value={query.authoritySearchId}
                                onChange={onChangeInputs}
                            >
                                <MenuItem key="-1" value="-1">
                                    {t("Tutte le autorità")}
                                </MenuItem>
                                {(calendarData.groups || []).map(
                                    (group: any, index: number) => (
                                        <MenuItem key={index} value={group.id}>
                                            {group.name}
                                        </MenuItem>
                                    )
                                )}
                            </Select>
                        </FormControl>
                    </Grid>
                )}
                <Grid item md={10} sx={{ mt: 2, mb: 2, display: 'flex', justifyContent: 'right', }}>
                    <Button
                        variant="text"
                        color="primary"
                        onClick={clearAll}
                        sx={{ textDecoration: 'underline', fontSize: 16, mb: 2 }}
                    >
                        {t("Cancella filtri")}
                    </Button>
                </Grid>
            </Grid>
        </Box>
    );
}
