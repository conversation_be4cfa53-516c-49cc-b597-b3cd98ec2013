import { useEffect, useState, useRef } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import FullCalendar from "@fullcalendar/react";
import { IQuery,ICalendarEvent } from "../typings/generalCalendar.interface";
import { debounce } from "lodash";
import { useLocation } from "react-router-dom";
import { formatTitle } from "../helpers/capitalizeItalianCalendar";
import { gettingCalendarView } from "../helpers/gettingCalendarViewName";
import moment from "moment";

const DEFAULT_QUERY: IQuery = {
    start: 0,
    end: 0,
    viewName: "month",
    date: 0,
    calendarCommitments: "-1",
    calendarPersons: [],
    calendarGroup: "-1",
    calendarReferent: "-1",
    calendarReferents: [],
    calendarAuthorities: "-1",
    calendarEvasa: [],
    calendarDeadlineType: [],
    calendarDeadlineCategory: [],
    calendarNonevadere: "-1",
    calendarWeekends: true,
    agendaView: false,
    onlyGroupEvents: false,
    deadlinesNotice: "1",
    hearingsNotice: "1",
    authoritySearchId: "",
    pratica: "",
    deadlineType: -1,
    deadlineCategory: -1,
    praticaSelectUniqueid: "",
    authority: "",
    visStart: 0,
    visEnd: 10,
    closeDeadline: true,
    deadlineTypes: [],
    deadlineCategories: [],
    important: false,
    private: false,
    calendarDeadlineTypeValues: [],
    calendarDeadlineCategoryValues: [],
    calendarEvasaValues: [],
    calendarPersonsValues: [],
};

export default function useGetEvents() {
    const calendarRef = useRef<FullCalendar>(null);
    const getEventsRequest = useGetCustom(
        "calendar/getevents?noTemplateVars=true"
    );
    const getCalendarRequest = useGetCustom("calendar/calendar");
    const location = useLocation();

    const queryFromLocalStorage = localStorage.getItem("agendaQuery");

    const [query, setQuery] = useState<any>(() => {
        if (queryFromLocalStorage && typeof queryFromLocalStorage !== undefined) {
            const savedQuery = JSON.parse(queryFromLocalStorage);

            return {
                ...savedQuery,
                date: moment().unix(),
                start: 0,
                end: 0
            };
        }
        return location?.state?.defaultParams ?? DEFAULT_QUERY;
    });
    const [calendarData, setCalendarData] = useState<any>([]);
    const [eventData, setEventData] = useState<ICalendarEvent[]>([]);
    const [eventResponse, setEventResponse] = useState<any>({});
    const [items, setItems] = useState<any[]>([]);
    const [monthTitle, setMonthTitle] = useState<string | undefined>("");

    //this useEffect is called only for inital render of the calendar
    useEffect(() => {
        async function fetchData() {
            const calendarAPI: any = calendarRef?.current?.getApi();

            const currentMonthTitle = calendarAPI?.view.title;
            const formattedTitle =
                currentMonthTitle?.charAt(0).toUpperCase() +
                currentMonthTitle?.slice(1);
            setMonthTitle(formattedTitle);

            const { data }: any = await getCalendarRequest.doFetch(true);
            setCalendarData(data);
            setItems(data.items);
            if (query.start === 0 && query.end === 0) {
                const updatedQuery = {
                    ...query,
                    start:
                        calendarAPI?.currentDataManager.state.dateProfile.activeRange.start.getTime() /
                        1000,
                    end:
                        calendarAPI?.currentDataManager.state.dateProfile.activeRange.end.getTime() /
                        1000,
                    date: (() => {
                        const serverDate = data.calendarDate ? Number(data.calendarDate) : query.date;
                        return serverDate;
                    })(),
                    calendarCommitments:
                        data.calendarCommitments === null
                            ? "-1"
                            : data.calendarCommitments,
                    calendarPersons:
                        localStorage.getItem("cleared") === "true"
                            ? []
                            : [data.calendarPerson], //render the initial calendar person
                    calendarEvasa: data.calendarEvasa,
                    agendaView: data.calendarAgendaView,
                    deadlinesNotice: data.commitmentsNotice,
                    hearingsNotice: data.hearingsNotice,
                };
                setQuery(updatedQuery);
            }
        }
        fetchData();
    }, []);

    // handlin the removed of the localstorage
    useEffect(() => {
        const handleBeforeUnload = () => {
            localStorage.removeItem("cleared");
        };
        window.addEventListener("beforeunload", handleBeforeUnload);
        return () =>
            window.removeEventListener("beforeunload", handleBeforeUnload);
    }, []);

    const fetchEventData = async (query?: IQuery) => {
        let calendarAPI: any = calendarRef?.current?.getApi();
        const getCalendarView = gettingCalendarView(query?.viewName ?? "");

        if (getCalendarView) {
            calendarAPI?.changeView(getCalendarView);
        }
        const currentMonthTitle = calendarAPI?.view.title;

        const formattedTitle = formatTitle(currentMonthTitle);
        setMonthTitle(formattedTitle);

        const apiParams: any = query ? {
            ...query,
            calendarPerson: query.calendarPersons && query.calendarPersons.length > 0 ? query.calendarPersons[0] : query.calendarCommitments
        } : {};

        const response: any = await getEventsRequest.doFetch(true, apiParams);
        setEventData(response.data as ICalendarEvent[]);
        setEventResponse(response); // Store the full response to access missedEvents
    };

    //this useEffect called only for filters
    useEffect(() => {
        const debouncedSearch = debounce(() => {
            fetchEventData(query);
        }, 500);

        debouncedSearch();

        return () => {
            debouncedSearch.cancel();
        };
    }, [
        query.calendarCommitments,
        query.calendarPerson,
        query.calendarPersons,
        query.calendarGroup,
        query.calendarReferent,
        query.calendarReferents,
        query.calendarAuthorities,
        query.calendarEvasa,
        query.calendarDeadlineType,
        query.calendarNonevadere,
        query.calendarWeekends,
        query.agendaView,
        query.onlyGroupEvents,
        query.deadlinesNotice,
        query.hearingsNotice,
        query.authoritySearchId,
        query.pratica,
        query.calendarDeadlineCategory,
        query.start,
    ]);

    return {
        calendarRef,
        query,
        eventData,
        eventResponse,
        calendarData,
        items,
        setQuery,
        fetchEventData,
        DEFAULT_QUERY,
        monthTitle,
        setMonthTitle,
    };
}
