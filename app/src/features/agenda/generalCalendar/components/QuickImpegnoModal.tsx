import {
    Button,
    FormControl,
    FormControlLabel,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    Divider,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormGroup,
    Toggle,
    Popover,
    Checkbox,
    FormHelperText,
    Autocomplete,
    VaporTag,
    Box
} from "@vapor/v3-components";
import { Close } from "@mui/icons-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBell } from "@fortawesome/pro-regular-svg-icons";
import DatePicker from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { TimePicker, AdapterDateFns, LocalizationProvider } from "@vapor/v3-components";
import { useEffect, useState, useCallback } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import { parseDate } from "../../../../helpers/parseDataFormat";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { formatTimeHHMM } from "../addCalendar/impegno/constants/constant";

interface IProps {
    open: boolean;
    selectedDate: any;
    wasOpen: boolean;
    handleModal: (event: any, reason: any, formData?: any) => void;
    view: string;
    savedFormData?: any;
    onFormDataChange?: (formData: any) => void;
    handleProperClose: () => void;
    fetchEventData: (query?: any) => Promise<void>;
    query: any;
    setOpenImpegnoModal: React.Dispatch<React.SetStateAction<boolean>>;
}

function parseTimeStringToDate(timeString: string | undefined | null) {
    console.log("parseTimeStringToDate", timeString);

    // Handle null, undefined, empty string, or just ":"
    if (!timeString || timeString === "" || timeString === ":") {
        return null;
    }

    const parts = timeString.split(":");
    if (parts.length !== 2) return null;

    const hh = parseInt(parts[0], 10);
    const mm = parseInt(parts[1], 10);

    if (isNaN(hh) || isNaN(mm) || hh < 0 || hh > 23 || mm < 0 || mm > 59) return null;

    const d = new Date();
    d.setHours(hh);
    d.setMinutes(mm);
    d.setSeconds(0);
    d.setMilliseconds(0);
    return d;
}

export const formatDateDDMMYYYY = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

const defaultParams = {
    deadlineText: "",
    deadlineType: "",
    deadlineDate: "",
    deadlineHours: "",
    deadlineMinutes: "",
    deadlinePeriod: "30", // in minutes
    deadlineUser: [],
    deadLinesGroups: "-1",
    nuovoImpegno: "0",
    dynamic: "0",
    deadlineOnorariDiritti: "0", // not needed but required by api
    deadlineSpeseesenti: "0", // not needed but required by api
    deadlineSpeseimponibili: "0", // not needed but required by api
    deadlineSpeseescluse: "0" // not needed but required by api
};

export default function QuickImpegnoModal(props: IProps) {
    const { open, selectedDate, handleModal, view, savedFormData, onFormDataChange, handleProperClose, fetchEventData, query, wasOpen, setOpenImpegnoModal } = props;
    const { t } = useTranslation();
    const [params, setParams] = useState<any>(savedFormData || defaultParams);
    const [anchorElGruppi, setAnchorElGruppi] = useState(null);
    const [anchorElIntestatari, setAnchorElIntestatari] = useState(null);
    const openGruppiPopover = Boolean(anchorElGruppi);
    const openIntestatariPopover = Boolean(anchorElIntestatari);
    const [tipologiaData, setTipologiaData] = useState([]);
    const [groupsOptions, setGroupsOptions] = useState<any>([]);
    const [userOptions, setUserOptions] = useState<any>([]);
    const [giornataSwitch, setGiornataSwitch] = useState(false); // Always default to false for new events
    const [showModal, setShowModal] = useState<boolean>(false);
    const [tempUsersSelected, setTempUsersSelected] = useState<any>([]);
    const [loggedUserName, setLoggedUserDataName] = useState<string>("");
    const [requiredFields, setRequiredFields] = useState({
        deadlineText: false,
        deadlineDate: false,
        deadlineType: false
    });

    useEffect(() => {
        console.log("QuickImpegnoModal savedFormData changed", savedFormData);
    }, []);

    const getCalendarDataRequest = useGetCustom("calendar/calendar");
    const getRowDataRequest = useGetCustom("deadlines/getrowdata?noTemplateVars=true");
    const getUsersGroupRequest = useGetCustom("deadlines/getusersgroup?noTemplateVars=true");

    const saveDeadlineRequest = usePostCustom("deadlines/save?noTemplateVars=true");

    const updateParams = (newParams: any) => {
        setParams(newParams);
        if (onFormDataChange) {
            onFormDataChange(newParams);
        }
    };

    const updateDefaultDeadlineType = (data: any) => {
        if (data) {
            const firstValue = data[0];
            setParams((prevParams: any) => ({
                ...prevParams,
                deadlineType: firstValue?.id
            }));
        }
    };

    useEffect(() => {
        const fetch = async () => {
            const [response, rowDataResponse]: any = await Promise.all([
                getCalendarDataRequest.doFetch(true),
                getRowDataRequest.doFetch(true, {
                    uniqueId: "",
                    fileUniqueid: ""
                })
            ]);
            const { deadlineTypes, loggedUser } = response.data;
            const { groups, users } = rowDataResponse.data;
            if (!wasOpen) {
                updateDefaultDeadlineType(deadlineTypes);
            }
            setTipologiaData(deadlineTypes);
            setGroupsOptions(groups);
            setUserOptions(users);
            setLoggedUserDataName(loggedUser?.nomeutente);
        };

        fetch();
    }, [wasOpen]);

    const onDateChange = (value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        updateParams((prevValue: any) => ({
            ...prevValue,
            deadlineDate: formattedDate
        }));
    };

    const onChangeInputs = (e: any) => {
        const { name, value } = e.target;

        if (value && value.length > 0 && requiredFields.deadlineText) {
            setRequiredFields({
                ...requiredFields,
                deadlineText: false
            });
        }

        updateParams((prev: any) => ({
            ...prev,
            [name]: value
        }));
    };

    const handleTimeChange = (newValue: any | null) => {
        if (!newValue || !(newValue instanceof Date) || isNaN(newValue.getTime())) {
            updateParams((prev: any) => ({
                ...prev,
                deadlineHours: "",
                deadlineMinutes: ""
            }));
            return;
        }
        const hh = String(newValue.getHours()).padStart(2, "0");
        const mm = String(newValue.getMinutes()).padStart(2, "0");

        updateParams((prev: any) => ({
            ...prev,
            deadlineHours: hh,
            deadlineMinutes: mm
        }));
    };

    const updateDeadlineUsers = (users: any) => {
        const uniqueUsers = Array.from(new Map(users.map((user: any) => [user.id, user])).values());

        // Directly update the `deadlineUser` with the unique list
        updateParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: uniqueUsers
        }));
    };

    const fetchUserGroupById = async (groupId: string) => {
        const response: any = await getUsersGroupRequest.doFetch(true, {
            idGruppo: groupId
        });
        return response.data;
    };

    const setCheckedUsersFromGroup = (groupData: any) => {
        const checkedUsers = groupData?.map((checkedUser: any) => userOptions?.find((user: any) => user.id === checkedUser.userId));
        updateParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: checkedUsers
        }));
    };

    const handleUsersGroup = async (_: any, newValue: any) => {
        const { id } = newValue;

        if (id !== "-1") {
            try {
                const groups = await fetchUserGroupById(id);

                updateParams((prevParams: any) => ({
                    ...prevParams,
                    deadLinesGroups: newValue,
                    deadlineUser: Array.isArray(groups) ? groups : [] // Ensure groups is an array
                }));

                if (Array.isArray(groups)) {
                    setCheckedUsersFromGroup(groups);
                }
            } catch (error) {
                console.error("Error fetching user group:", error);
                // Handle error case
            }
        } else {
            updateParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti...")
                },
                deadlineUser: id === "-1" ? [] : prevParams.deadlineUser // Keep existing users if not resetting
            }));
        }
    };

    const handleUserData = (_: any, newValue: any) => {
        if (params.deadLinesGroups && (params.deadLinesGroups === "-1" || params.deadLinesGroups.id === "-1")) {
            // Ensure uniqueness by `id` only
            updateDeadlineUsers(newValue);
        } else {
            setTempUsersSelected(newValue);
            setShowModal(true);
        }
    };

    const handleModalConfirm = (confirm: boolean) => {
        if (confirm) {
            updateParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti...")
                }
            }));

            if (tempUsersSelected.length > 0) {
                updateDeadlineUsers(tempUsersSelected);
            }
        }
        setShowModal(false);
    };

    const updateDefaultDeadlineUser = useCallback((data: any, loggedUserName: string) => {
        // For new impegno, use the current logged-in user
        const loggedUser = data?.find((user: any) => user.nomeutente === loggedUserName);

        if (loggedUser && !wasOpen) {
            updateParams((prevParams: any) => ({
                ...prevParams,
                deadlineUser: [
                    {
                        id: loggedUser.id,
                        nomeutente: loggedUser.nomeutente
                    }
                ]
            }));
        }
    }, []);

    // Reset the toggle when modal opens
    useEffect(() => {
        if (open) {
            setGiornataSwitch(false);
            sessionStorage.removeItem("giornataSwitchImpegno");
        }
    }, [open]);

    const handleGiornataSwitch = (event: any) => {
        const { checked } = event.target;
        setGiornataSwitch(checked);
        if (checked) {
            updateParams((prev: any) => ({
                ...prev,
                deadlinePeriod: "0",
                deadlineHours: "",
                deadlineMinutes: ""
            }));
        }
    };

    useEffect(() => {
        if (loggedUserName) {
            updateDefaultDeadlineUser(userOptions, loggedUserName);
        }
    }, [loggedUserName]);

    // Also fix the useEffect that calls formatTimeHHMM
    useEffect(() => {
        if (wasOpen) return;

        const dateToUse = view === "dayGridMonth" ? new Date() : selectedDate || new Date();
        // Ensure we have a valid date before calling formatTimeHHMM
        const validDate = dateToUse instanceof Date && !isNaN(dateToUse.getTime()) ? dateToUse : new Date();
        const { hours, minutes } = formatTimeHHMM(validDate);

        updateParams((prev: any) => ({
            ...prev,
            deadlineDate: formatDateDDMMYYYY(validDate),
            deadlineHours: hours,
            deadlineMinutes: minutes
        }));
    }, [selectedDate, wasOpen]);

    const saveDeadline = async (deadlineSaveParams: any) => {
        if (deadlineSaveParams.deadlineText === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineText: true
            });
            return;
        }

        if (deadlineSaveParams.deadlineDate === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineDate: true
            });
            return;
        }

        if (deadlineSaveParams.deadlineType === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineType: true
            });
            return;
        }

        // If giornataSwitch is true, force duration to 24h (1440 minutes)
        const paramsToSend = {
            ...deadlineSaveParams,
            deadlinePeriod: giornataSwitch ? "1440" : deadlineSaveParams.deadlinePeriod,
            deadlineHours: giornataSwitch ? "00" : deadlineSaveParams.deadlineHours,
            deadlineMinutes: giornataSwitch ? "00" : deadlineSaveParams.deadlineMinutes
        };

        const formData = new FormData();
        const deadlineUser: any = [];

        Object.keys(paramsToSend).forEach((key) => {
            const value = paramsToSend[key as keyof any];

            if (key === "deadlineUser" && Array.isArray(value)) {
                value.forEach((user: any) => {
                    formData.append("deadlineUser[]", user.id);
                    deadlineUser.push(user.id);
                });
                const userIds = value.map((user: any) => user.id).join(",");
                formData.append("deadlineUserData", userIds);
                return;
            }

            if (key === "deadLinesGroups" && value?.id) {
                formData.append("deadLinesGroups", value.id);
                return;
            }
            if (key !== "deleteActivityFunction") {
                formData.append(key, value);
            }
        });
        // Only add giornata_intera parameter when it's actually a full-day event
        // Backend checks for parameter existence, not value
        if (giornataSwitch) {
            formData.append("giornata_intera", "1");
        }

        const response: any = await saveDeadlineRequest.doFetch(true, formData);
        if (response.status === 200) {
            await fetchEventData(query);

            // Delete the activity after successful engagement creation
            if (savedFormData?.activityId && savedFormData?.deleteActivityFunction) {
                try {
                    await savedFormData.deleteActivityFunction(savedFormData.activityId);
                    console.log("Activity deleted successfully after engagement creation");
                } catch (error) {
                    console.error("Error deleting activity after engagement creation:", error);
                }
            }

            handleProperClose();
            sessionStorage.removeItem("giornataSwitchImpegno");
        }
    };

    const handleClose = (event: any, reason: any) => {
        if (reason === "backdropClick") {
            handleModal(event, reason, params);
        } else {
            handleModal(event, reason);
        }
        // Always reset the toggle for next time
        setGiornataSwitch(false);
        sessionStorage.removeItem("giornataSwitchImpegno");
    };

    const minutesToDate = (totalMinutes: number) => {
        const d = new Date();
        d.setHours(0, totalMinutes, 0, 0);
        return d;
    };

    const dateToMinutes = (d: Date | null) => {
        if (!d) return 0;
        return d.getHours() * 60 + d.getMinutes();
    };

    const goToFullImpengoModal = () => {
        // Save params to sessionStorage for the full modal
        sessionStorage.setItem("fullImpegnoInitialData", JSON.stringify(params));
        sessionStorage.setItem("giornataSwitchImpegno", JSON.stringify(giornataSwitch));

        if (savedFormData.deleteActivityFunction !== undefined) sessionStorage.setItem("deleteActivityId", savedFormData.activityId);
        handleProperClose();
        setOpenImpegnoModal(true);
        // No need to pass params via props anymore
    };

    return (
        <Dialog
            open={open}
            keepMounted
            onClose={handleClose}
            aria-describedby="alert-dialog-description"
            aria-labelledby="alert-dialog-title"
            scroll="paper" // ensure that DialogContent becomes the scroll container
            PaperProps={{
                sx: {
                    height: "635px",
                    maxHeight: "95vh",
                    width: "500px"
                }
            }}
        >
            <DialogTitle sx={{ p: 0.5, pl: 2 }}>
                {t("Nuovo impegno")}
                <IconButton
                    color="primary"
                    onClick={() => {
                        handleProperClose();
                        setGiornataSwitch(false); // Reset toggle for next time
                        sessionStorage.removeItem("giornataSwitchImpegno");
                    }}
                    variant="text"
                >
                    <Close />
                </IconButton>
            </DialogTitle>
            <Divider variant="fullWidth" />{" "}
            <DialogContent>
                <Box width="100%">
                    <TextField
                        fullWidth
                        label={t("Titolo *")}
                        variant="outlined"
                        margin="normal"
                        name="deadlineText"
                        value={params.deadlineText}
                        onChange={onChangeInputs}
                        error={!!requiredFields.deadlineText}
                        helperText={requiredFields.deadlineText ? t("Titolo obbligatorio") : ""}
                    />
                    <Box
                        sx={{
                            display: "flex",
                            gap: 2 // space between the two FormControls
                        }}
                    >
                        <FormControl
                            sx={{ width: "405px" }}
                            margin="normal"
                            error={!!requiredFields.deadlineType} // 1. Add error prop to FormControl
                        >
                            <InputLabel>{t("Tipologia *")}</InputLabel>
                            <Select name="deadlineType" value={params.deadlineType} onChange={onChangeInputs}>
                                <MenuItem value="-1">Generica</MenuItem>
                                {tipologiaData.map((type: any, index: number) => (
                                    <MenuItem value={type.id} key={index}>
                                        {type.nome}
                                    </MenuItem>
                                ))}
                            </Select>
                            <FormHelperText>{requiredFields.deadlineType ? t("Tipologia obbligatoria") : ""}</FormHelperText>
                        </FormControl>
                        <FormControl
                            fullWidth
                            margin="normal"
                            error={!!requiredFields.deadlineType} // 1. Add error prop to FormControl
                        >
                            <InputLabel>{t("Impegno Standard")}</InputLabel>
                            <Select>
                                <MenuItem value="-1">None</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 2,
                            width: "100%",
                            mb: 2,
                            mt: 1,
                            flexWrap: "nowrap",
                            alignItems: "center"
                        }}
                    >
                        {/* full‐width date */}
                        <DatePicker label="" name="deadlineDate" value={parseDate(params?.deadlineDate)} onChange={onDateChange} sx={{ width: "370px" }} />

                        {/* first time – half width */}
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                value={parseTimeStringToDate(`${params.deadlineHours || "00"}:${params.deadlineMinutes || "00"}`)}
                                onChange={handleTimeChange}
                                disabled={giornataSwitch}
                                ampm={false}
                                slotProps={{
                                    textField: {
                                        placeholder: "HH:MM",
                                        sx: { width: "200px" }
                                    }
                                }}
                            />
                        </LocalizationProvider>

                        {/* second time – also half width */}
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                disabled={giornataSwitch}
                                ampm={false}
                                slotProps={{
                                    textField: {
                                        placeholder: "HH:MM",
                                        sx: { width: "200px" }
                                    }
                                }}
                            />
                        </LocalizationProvider>
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 7,
                            alignItems: "center"
                            // mb: 2,
                        }}
                    >
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                label={t("Durata")}
                                disabled={giornataSwitch}
                                value={minutesToDate(params.deadlinePeriod)}
                                onChange={(newValue: any) => {
                                    const mins = dateToMinutes(newValue);
                                    setParams((old: any) => ({
                                        ...old,
                                        deadlinePeriod: mins
                                    }));
                                }}
                                ampm={false}
                                views={["hours", "minutes"]}
                                slotProps={{
                                    textField: {
                                        name: "",
                                        placeholder: "HH:MM",
                                        sx: { width: "215px !important" }
                                    }
                                }}
                            />
                        </LocalizationProvider>

                        <FormControl component="fieldset" variant="standard">
                            <FormGroup>
                                <FormControlLabel
                                    control={<Toggle size="small" checked={giornataSwitch} onChange={handleGiornataSwitch} />}
                                    label={t("Giornata intera")}
                                    sx={{
                                        ml: 6,
                                        mt: 2.5,
                                        whiteSpace: "nowrap"
                                    }}
                                />
                            </FormGroup>
                        </FormControl>
                    </Box>
                    <Divider light sx={{ mt: 3 }} />
                    <Box
                        sx={{
                            display: "flex",
                            gap: 1,
                            alignItems: "center"
                        }}
                    >
                        <FormControl fullWidth sx={{ mt: 2 }}>
                            <Autocomplete
                                options={[
                                    {
                                        id: "-1",
                                        name: t("Seleziona il gruppo di utenti...")
                                    },
                                    ...(groupsOptions || [])
                                ]}
                                value={
                                    params.deadLinesGroups === "-1"
                                        ? {
                                              id: "-1",
                                              name: t("Seleziona il gruppo di utenti...")
                                          }
                                        : params.deadLinesGroups
                                }
                                isOptionEqualToValue={(option: any, value: any) => {
                                    return option.id === value.id;
                                }}
                                limitTags={1}
                                onChange={handleUsersGroup}
                                getOptionLabel={(option: any) => option?.name}
                                renderInput={(params: any) => <TextField {...params} size="medium" label={t("Gruppo Utenti")} />}
                            />
                        </FormControl>
                        <IconButton onClick={(event: any) => setAnchorElGruppi(event.currentTarget)} sx={{ marginTop: "40px" }} variant="outlined">
                            <FontAwesomeIcon icon={faBell} fontSize="15px" size="sm" />{" "}
                        </IconButton>
                        <Popover
                            open={openGruppiPopover}
                            anchorEl={anchorElGruppi}
                            onClose={() => setAnchorElGruppi(null)}
                            anchorOrigin={{
                                vertical: "bottom",
                                horizontal: "left"
                            }}
                            PaperProps={{
                                sx: { p: 2, borderRadius: 2 }
                            }}
                        >
                            <FormGroup sx={{ p: 1 }}>
                                <FormControlLabel sx={{ mb: 1 }} control={<Checkbox onChange={() => ""} name="notify" color="primary" />} label={t("Invia notifica")} />
                                <FormControlLabel control={<Checkbox onChange={() => ""} name="email" color="primary" />} label={t("Invia email")} />
                            </FormGroup>
                        </Popover>
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            gap: 1,
                            alignItems: "center"
                        }}
                    >
                        <FormControl fullWidth sx={{ mt: 2 }}>
                            <Autocomplete
                                disableCloseOnSelect
                                multiple
                                options={userOptions || []}
                                value={params.deadlineUser}
                                onChange={handleUserData}
                                isOptionEqualToValue={(option: any, value: any) => {
                                    return option.id === value.id;
                                }}
                                getOptionLabel={(option: any) => option.nomeutente}
                                renderInput={(params: any) => <TextField {...params} label={t("Intestatari")} />}
                                renderOption={(props, option, { selected }) => (
                                    <li {...props}>
                                        <Checkbox size="small" style={{ marginRight: 8 }} checked={selected} />
                                        {option.nomeutente}
                                    </li>
                                )}
                                noOptionsText={t("Nessuna opzione")}
                                renderTags={(value: any[], getTagProps: any) => {
                                    return value.map((option, index) => {
                                        const { key, ...tagProps } = getTagProps({
                                            index
                                        });
                                        return (
                                            <div
                                                key={key}
                                                style={{
                                                    marginRight: "2px",
                                                    marginBottom: "2px",
                                                    marginTop: "2px"
                                                }}
                                            >
                                                <VaporTag key={key} label={option.nomeutente} variant="filter" {...tagProps} />
                                            </div>
                                        );
                                    });
                                }}
                            />
                        </FormControl>
                        <IconButton onClick={(event: any) => setAnchorElIntestatari(event.currentTarget)} sx={{ marginTop: "40px" }} variant="outlined">
                            <FontAwesomeIcon icon={faBell} fontSize="15px" size="sm" />{" "}
                        </IconButton>
                        <Popover
                            open={openIntestatariPopover}
                            anchorEl={anchorElIntestatari}
                            onClose={() => setAnchorElIntestatari(null)}
                            anchorOrigin={{
                                vertical: "bottom",
                                horizontal: "left"
                            }}
                            PaperProps={{
                                sx: { p: 2, borderRadius: 2 }
                            }}
                        >
                            <FormGroup sx={{ p: 1 }}>
                                <FormControlLabel sx={{ mb: 1 }} control={<Checkbox onChange={() => ""} name="notify" color="primary" />} label={t("Invia notifica")} />
                                <FormControlLabel control={<Checkbox onChange={() => ""} name="email" color="primary" />} label={t("Invia email")} />
                            </FormGroup>
                        </Popover>
                    </Box>
                </Box>
            </DialogContent>
            <DialogActions
                sx={{
                    display: "flex",
                    alignItems: "end",
                    justifyContent: "end"
                }}
            >
                <Stack direction="row">
                    <Button size="small" onClick={goToFullImpengoModal} sx={{ mr: 2 }}>
                        {t("Altre opzioni")}
                    </Button>
                    <Button
                        variant="contained"
                        size="small"
                        sx={{
                            ml: 1
                        }}
                        onClick={() => saveDeadline(params)}
                    >
                        {t("Salva")}
                    </Button>
                </Stack>
            </DialogActions>
            <ConfirmModal
                open={showModal}
                handleDecline={() => handleModalConfirm(false)}
                handleAgree={() => handleModalConfirm(true)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t("Stai associando manualmente le persone al gruppo. L'associazione di gruppo verra tolta.")}
                title={t("Vuoi continuare?")}
            />
        </Dialog>
    );
}
