import {
    <PERSON><PERSON><PERSON>,
    Box,
    FormControl,
    InputLabel,
    TextField,
    RadioGroup,
    FormControlLabel,
    Radio,
    Button,
    Autocomplete,
    Tooltip,
} from "@vapor/v3-components";
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import { UseFormReturn } from "react-hook-form";
import { useState, useEffect, useMemo, useCallback } from "react";
import { createFilterOptions } from "@mui/material/Autocomplete";
import ContentHeader from "../../components/ContentHeader";
import usePostCustom from "../../../../hooks/usePostCustom";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import { checkData } from "../../utils/validation-check";
import { debounce } from "lodash";
// import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

interface IDatiGenerali {
    anagraficheData: any;
    t: any;
    method: UseFormReturn;
    isSociety: boolean;
    isPersonaFisica: boolean;
    userType: any;
    setUserType: any;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: any;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
    optionalFields?: any;
    handleSaveAttempt?: (fromSection?: string) => Promise<boolean>;
}

interface City {
    id: number;
    nome: string;
}

const filter = createFilterOptions<any>();

export default function DatiGeneraliForm(props: IDatiGenerali) {
    const {
        anagraficheData = [],
        method,
        t,
        isUpdate = false,
        showCreateForm,
        setShowCreateForm,
        onSubmitUpdate,
        isPersonaFisica,
        isSociety,
        userType,
        setUserType,
        handleSaveAttempt
    }: any = props;


    const {
        register,
        setValue,
        formState: { errors },
        watch,
        handleSubmit,
        setError,
        clearErrors,
    } = method;


    const denuminazione = watch("nome");
    const nome = watch("subjectName");
    const cognome = watch("subjectSurname");




    useEffect(() => {
        if (isSociety || isPersonaFisica) {
            clearErrors(["subjectName", "subjectSurname"]);
        }

        if (!isSociety) {
            !nome && isUpdate && setError("subjectName", { message: "Nome obbligatorio" });
            !cognome && isUpdate && setError("subjectSurname", { message: "Cognome obbligatorio" });
        }

        if (!denuminazione && isSociety && isUpdate) {
            setError("nome", { message: "Denominazione obbligatoria" });

        } else {
            clearErrors("nome");
        }
    }, [denuminazione, nome, cognome, isSociety]);


    const values = watch();
    const navigate = useNavigate();
    const { titles, cities } = anagraficheData;
    const [hanldeCityValues, setHanldeCityValue] = useState<City[]>([]);
    const [codiceFiscaleError, setCodiceFiscaleError] = useState<string>('');
    const [partitaIvaError, setPartitaIvaError] = useState<string[]>([]);

    const correctCities = useMemo(
        () => (cities === undefined ? [] : cities),
        [cities]
    );

    const fiscalCodeRequest = usePostCustom(
        "anagrafiche/fiscal-code?noTemplateVars=true"
    );


    const checkPartitaIvaRequest = usePostCustom(
        "anagrafiche/checkdata?noTemplateVars=true"
    );

    useEffect(() => {
        if (hanldeCityValues !== correctCities) {
            setHanldeCityValue(correctCities);
        }
    }, [correctCities, hanldeCityValues]);
    


    const handleSaveChanges = async (event: any) => {
        try {
            event.preventDefault();

            if (handleSaveAttempt) {
                const canSave = await handleSaveAttempt('datiGenerali');
                console.log({canSave})
                if (!canSave) {
                    // Validation failed, errors are already set
                    return;
                }
            }   
            if (Object.keys(errors).length > 0) {
                return;
            }

            if (onSubmitUpdate && typeof onSubmitUpdate === 'function') {
                handleSubmit(onSubmitUpdate)();
            }

            setShowCreateForm({
                ...showCreateForm,
                datiGenerali: false,
            });
        } catch (error) {
            console.error(error);
        }
    };

    const navigateToLegacy = () => {
        const isConfirm = confirm(
            "Si verrà reindirizzati sull'inserimento città, le modifiche non salvate verranno perse. Continuare?"
        );

        if (isConfirm) {
            navigate("/legacy/cities/update?anagrafica=-1&uid=");
        }
    };

    const calculateFiscalCode = async () => {
        const birthplace = hanldeCityValues.find(
            (city: any) => city.id === values.luogonascita
        )?.nome;
        const birthdate = moment(values.datanascita).format("DD/MM/YYYY");
        const params = {
            name: values.subjectName,
            surname: values.subjectSurname,
            gender: values.subjectGender,
            birthdate,
            birthplace,
        };

        try {
            const response: any = await fiscalCodeRequest.doFetch(true, params);
            setValue(
                "codicefiscale",
                response ? response.data.fiscal_code : ""
            );
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        setValue("tipo", userType?.detailSelected);
    }, []);

    const validateCodiceFiscale = (value: string) => {
        if (!value) {
            setCodiceFiscaleError('');
            return true;
        }

        const alphanumericRegex = /^[a-zA-Z0-9]+$/;

        if (value.length < 11) {
            setCodiceFiscaleError('Il Codice Fiscale inserito potrebbe non essere corretto.');
            return false;
        }

        if (value.length > 16) {
            setCodiceFiscaleError('Il Codice Fiscale inserito potrebbe non essere corretto.');
            return false;
        }

        if (!alphanumericRegex.test(value)) {
            setCodiceFiscaleError('Il Codice Fiscale inserito potrebbe non essere corretto.');
            return false;
        }

        setCodiceFiscaleError('');
        return true;
    };

    const validatePartitaIva = async (value: string) => {
        const errors: string[] = [];

        if (!value) {
            setPartitaIvaError([]);
            return true;
        }

        const alphanumericRegex = /^[a-zA-Z0-9]+$/;

        if (value.length !== 11) {
            errors.push('La Partita Iva inserita potrebbe non essere corretta.');
        }

        if (!alphanumericRegex.test(value)) {
            errors.push('La Partita Iva inserita potrebbe non essere corretta.');
        }

        try {
            const response: any = await checkData(checkPartitaIvaRequest, watch());

            if (response?.optional?.partitaiva === "partitaivaExists") {
                errors.push("La Partita Iva è già associata ad un'altra anagrafica.\n\n");
            }

        } catch (error) {
            console.error('Error checking duplicate Partita IVA:', error);
        }

        setPartitaIvaError(errors);
        return errors.length === 0;
    };

    const debouncedValidatePartitaIva = useCallback(
        debounce(async (value: string) => {
            await validatePartitaIva(value);
        }, 800),
        []
    );

    useEffect(() => {
        return () => {
            debouncedValidatePartitaIva.cancel();
        };
    }, [debouncedValidatePartitaIva]);

    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1,
            }}
        >
            <Box display="flex">
                <ContentHeader
                    icon={faIdCard}
                    title={t("Dati generali")}
                />
            </Box>

            <Autocomplete
                sx={{ width: 815 }}
                options={[
                    { id: "0", name: t("Non specificata") },
                    ...(userType?.details || [])
                ]}
                getOptionLabel={(option) => option.name}
                value={[
                    { id: "0", name: t("Non specificata") },
                    ...(userType?.details || [])
                ].find((option: any) => option.id === values.tipo) || null}
                onChange={(_, newValue) => {
                    const selectedValue = newValue ? newValue.id : "0";
                    setValue("tipo", selectedValue);
                    setUserType({
                        ...userType,
                        detailSelected: selectedValue,
                    });
                }}
                renderInput={(params) => (
                    <TextField {...params} label={t("Dettaglio tipologia di anagrafica")} />
                )}
            />

            <Typography variant="subtitle" color="primary.textTitleColor">
                {isPersonaFisica ? t("Dati soggetto") : t("Dati società")}
            </Typography>

            <FormControl sx={{ width: 815 }}>
                {isPersonaFisica && (
                    <>
                        <InputLabel>{t("Titolo")}</InputLabel>
                        <Autocomplete
                            options={titles || []}
                            getOptionLabel={(option) => option.nome}
                            value={
                                titles?.find(
                                    (t: any) => t.id === values.titolo
                                ) || null
                            }
                            onChange={(_, newValue) => {
                                setValue("titolo", newValue ? newValue.id : "");
                            }}
                            renderInput={(params) => (
                                <TextField {...params} variant="outlined" />
                            )}
                        />
                    </>
                )}
            </FormControl>
            <Box sx={{ display: "flex", gap: 2 }}>
                {!isPersonaFisica ? (
                    <TextField
                        label={t("Denominazione*")}
                        sx={{ width: 815 }}
                        {...register("nome")}
                        value={values.nome}
                        error={errors.nome ? true : false}
                        helperText={errors.nome?.message}
                    />
                ) : (
                    <>
                        <TextField
                            label={t("Nome*")}
                            sx={{ width: 400 }}
                            {...register("subjectName")}
                            value={values.subjectName}
                            error={errors.subjectName ? true : false}
                            helperText={errors.subjectName?.message}
                        />
                        <TextField
                            label={t("Cognome*")}
                            sx={{ width: 400 }}
                            {...register("subjectSurname")}
                            value={values.subjectSurname}
                            error={errors.subjectSurname ? true : false}
                            helperText={errors.subjectSurname?.message}
                        />
                    </>
                )}
            </Box>
            {isPersonaFisica && (
                <>
                    <FormControl
                        sx={{ width: 400, ml: 1 }}
                        component="fieldset"
                    >
                        <Typography>{t("Sesso")}</Typography>
                        <RadioGroup row>
                            <FormControlLabel
                                value="M"
                                control={
                                    <Radio
                                        checked={values.subjectGender === "M"}
                                    />
                                }
                                label={t("Maschio")}
                                {...register("subjectGender")}
                                name="subjectGender"
                            />
                            <FormControlLabel
                                value="F"
                                control={
                                    <Radio
                                        checked={values.subjectGender === "F"}
                                    />
                                }
                                label={t("Femmina")}
                                {...register("subjectGender")}
                                name="subjectGender"
                            />
                        </RadioGroup>
                    </FormControl>
                    <Box sx={{ display: "flex", gap: 2 }}>
                        <Autocomplete
                            sx={{ width: 400 }}
                            options={hanldeCityValues}
                            filterOptions={(options: any, params: any) => {
                                const filtered = filter(options, params);
                                const { inputValue } = params;
                                const isExisting = options.some(
                                    (option: any) => inputValue === option.nome
                                );
                                if (inputValue !== "" && !isExisting) {
                                    filtered.push({
                                        inputValue: inputValue,
                                        nome: `Aggiungi "${inputValue}"`,
                                    });
                                }
                                return filtered;
                            }}
                            value={
                                hanldeCityValues.find(
                                    (option: any) =>
                                        option.id === values?.luogonascita
                                ) || null
                            }
                            onChange={(_: any, value: any) => {
                                if (value && value.inputValue) {
                                    navigateToLegacy();
                                } else {
                                    setValue("luogonascita", value.id);
                                }
                            }}
                            getOptionLabel={(option: any) => {
                                if (typeof option === "string") {
                                    return option;
                                }
                                if (option?.nome?.startsWith(t('Aggiungi "'))) {
                                    return option.inputValue;
                                }
                                return option.nome;
                            }}
                            renderOption={(props: any, option: any) => {
                                if (
                                    option.nome &&
                                    option.nome.startsWith(t('Aggiungi "'))
                                ) {
                                    return (
                                        <li {...props}>
                                            <Typography>
                                                {option.nome}
                                            </Typography>
                                        </li>
                                    );
                                }
                                return <li {...props}>{option.nome}</li>;
                            }}
                            renderInput={(params: any) => (
                                <TextField
                                    {...params}
                                    label={t("Luogo di nascita")}
                                />
                            )}
                        />
                        <DatePicker
                            label={t("Data di nascita")}
                            sx={{ width: 400 }}
                            value={
                                values.datanascita ? values.datanascita : null
                            }
                            onChange={(value: Date | null) => {
                                setValue("datanascita", value);
                            }}
                        />
                    </Box>
                </>
            )}
            <Box sx={{ display: "flex", gap: 2 }}>
                <TextField
                    label={t("Codice Fiscale")}
                    name="codicefiscale"
                    sx={{
                        width: codiceFiscaleError ? 362 : 400,
                        ...(codiceFiscaleError && {
                            '& .MuiOutlinedInput-root': {
                                '& fieldset': {
                                    borderColor: 'warning.main',
                                    borderWidth: '2px',
                                },
                                '&:hover fieldset': {
                                    borderColor: 'warning.main',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: 'warning.main',
                                },
                            },
                        })
                    }}
                    {...register("codicefiscale", {
                        validate: validateCodiceFiscale
                    })}
                    value={values.codicefiscale}
                    error={errors.codicefiscale ? true : false}
                    helperText={errors.codicefiscale?.message}
                    onChange={(e) => {
                        setValue("codicefiscale", e.target.value);
                        validateCodiceFiscale(e.target.value);
                    }}
                />
                {codiceFiscaleError && (
                    <Tooltip
                        title='Attenzione!'
                        arrow
                        placement="right"
                        description={codiceFiscaleError}
                    >
                        <ReportProblemIcon
                            sx={{
                                color: 'warning.main',
                                cursor: "pointer",
                                marginTop: 3.5,
                            }}
                        />
                    </Tooltip>
                )}
                <TextField
                    label={t("Partita Iva")}
                    name="partitaiva"
                    sx={{
                        width: partitaIvaError.length > 0 ? 362 : 400,
                        ...(partitaIvaError.length > 0 && {
                            '& .MuiOutlinedInput-root': {
                                '& fieldset': {
                                    borderColor: 'warning.main',
                                    borderWidth: '2px',
                                },
                                '&:hover fieldset': {
                                    borderColor: 'warning.main',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: 'warning.main',
                                },
                            },
                        })
                    }}
                    {...register("partitaiva", {
                        validate: validatePartitaIva
                    })}
                    value={values.partitaiva}
                    error={errors.partitaiva ? true : false}
                    helperText={errors.partitaiva?.message}
                    onChange={(e) => {
                        setValue("partitaiva", e.target.value);
                        if (partitaIvaError.length > 0) {
                            setPartitaIvaError([]);
                        }
                        debouncedValidatePartitaIva(e.target.value);
                    }}
                />
                {partitaIvaError.length > 0 && (
                    <Tooltip
                        title='Attenzione!'
                        arrow
                        placement="right"
                        description={partitaIvaError.join('\n\n')}
                    >
                        <ReportProblemIcon
                            sx={{
                                color: 'warning.main',
                                cursor: "pointer",
                                marginTop: 3.5,
                            }}
                        />
                    </Tooltip>
                )}
            </Box>
            {isPersonaFisica && (
                <Box>
                    <Button sx={{ marginTop: 1 }} onClick={calculateFiscalCode}>
                        {t("Calcola Codice Fiscale")}
                    </Button>
                </Box>
            )}
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815,
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                datiGenerali: false,
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
