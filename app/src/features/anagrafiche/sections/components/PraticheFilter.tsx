import {  useState, useCallback } from "react";
import { Box, Button, Grid, FormControlLabel, Checkbox, SearchBar, Autocomplete, TextField } from "@vapor/v3-components";
import { Filter } from "@vapor/react-icons";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { SelectMultiple } from "../../../../custom-components/SelectMultiple";
import { processDateValue } from "../../../../helpers/dateHelper";
import { debounce } from "lodash";

const defaultParams = {
    peopleUniqueid: "",
    page: 0,
    pageSize: 7,
    sortColumn: "codicepratica",
    sortOrder: "asc",
    itemStartDate: "",
    itemEndDate: "",
    situazione: -1,
    situazioneContabile: -1,
    dateType: 0,
    startDate: "",
    endDate: "",
    searchField: "",
    status: [],
    categoryIds: [],
};

const cancelQueryButtonStyle = {
    marginTop: "auto",
    marginLeft: "1rem",
};
interface PraticheFilterProps {
    customersFileQuery: any;
    setCustomerFileQuery: (queryParams: any) => void;
}

export default function PraticheFilter({
    customersFileQuery,
    setCustomerFileQuery,
    
}: PraticheFilterProps) {
    const { t } = useTranslation();
    const { anagrafiche } = useAnagraficheProvider();
    const [showAdditionalFields, setShowAdditionalFields] = useState(false);
    const { customer } = anagrafiche;
    const [searchValue, setSearchValue] = useState(customersFileQuery.searchField || "");
    const [date, setDate] = useState({
        itemStartDate: "",
        itemEndDate: "",
        startDate: "",
        endDate: "",
     });
    const [categorySelectedIds, setCategorySelectedIds] = useState<any[]>([]);
    const [statusSelectedIds, setStatusSelectedIds] = useState<any[]>([]);

    // Create debounced search function
    const debouncedSearch = useCallback(
        debounce((searchTerm: string) => {
            setCustomerFileQuery((prev: any) => ({
                ...prev,
                searchField: searchTerm
            }));
        }, 1000),
        [setCustomerFileQuery]
    );

    // Handle search input change
    const handleSearchChange = (value: any) => {
        const searchTerm = typeof value === 'string' ? value : value?.target?.value || '';
        setSearchValue(searchTerm);
        debouncedSearch(searchTerm);
    };

    const handleInputChange = (e: any) => {
        const { name, value } = e.target;
        const updatedFilters = {
            [name as string]: value,
        };
        setCustomerFileQuery((prev: any) => ({ ...prev, ...updatedFilters }));
    };

    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        if (checked) {
            setCustomerFileQuery((prev: any) => ({ ...prev, [name]: "on" }));
        } else {
            setCustomerFileQuery((prev: any) => {
                const newQuery = { ...prev };
                delete newQuery[name];
                return newQuery;
            });
        }
    };

      const handleReset = () => {
        setDate({
            itemStartDate: "",
            itemEndDate: "",
            startDate: "",
            endDate: "",
        });
        setCategorySelectedIds([]);
        setStatusSelectedIds([]);
        setSearchValue("");
        setCustomerFileQuery({
            ...defaultParams,
            peopleUniqueid: customersFileQuery.peopleUniqueid || customer?.uniqueid || ""
        });
        
    }

    const onDateChange = (name: string, value: Date) => {
        const formattedDate = value ? value.toLocaleDateString("en-GB") : "";
        const updatedFilters = {
            [name]: formattedDate,
        };
        setDate((prev: any) => ({ ...prev, ...updatedFilters }));
        setCustomerFileQuery((prev: any) => ({ ...prev, ...updatedFilters }));
    };

    const handleMultiSelectChange = (_: any, value: any[], name: string) => {
        if (name === 'status') {
            setStatusSelectedIds(value);
            setCustomerFileQuery((prev: any) => ({
                ...prev,
                status: value.map(item => item.id)
            }));
        } else if (name === 'categoryIds') {
            setCategorySelectedIds(value);
            setCustomerFileQuery((prev: any) => ({
                ...prev,
                categoryIds: value.map(item => item.id)
            }));
        }
    };

    const toggleAdditionalFields = () => {
        setShowAdditionalFields(!showAdditionalFields);
    }

    return (
        <Box sx={{ width: '100%', p: 2 }}>
            <Grid container spacing={2}>
                <Grid item xs={12} md={4} sx={{ mt: 2.8 }}>
                    <SearchBar
                        placeholder={t("Ricerca pratica")}
                        value={searchValue}
                        sx={{
                            width: "100%"
                        }}
                        onChange={handleSearchChange}
                        fullWidth
                    />
                </Grid>

                <Grid item xs={12} md={2.5}>
                    <SelectMultiple
                        style={{ width: "100%" }}
                        width="100%"
                        name="status[]"
                        label={t("Stato")}
                        options={anagrafiche?.filesStatus || []}
                        onChange={(event: any, newValue: any) => handleMultiSelectChange(event, newValue, 'status')}
                        selectedValues={statusSelectedIds}
                        placeholder={t("Stato")} />
                </Grid>

                <Grid item xs={12} md={2.5}>
                    <Autocomplete
                        fullWidth
                        options={[
                            { id: -1, nome: t("Tutti") },
                            { id: 1, nome: t("Non assegnati") },
                            ...(anagrafiche?.situazione || [])
                        ]}
                        getOptionLabel={(option) => option.nome}
                        value={[
                            { id: -1, nome: t("Tutti") },
                            { id: 1, nome: t("Non assegnati") },
                            ...(anagrafiche?.situazione || [])
                        ].find((status: any) => status.id === customersFileQuery.situazione) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "situazione",
                                    value: newValue ? newValue.id : -1
                                }
                            };
                            handleInputChange(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Situazione")} />
                        )}
                    />
                </Grid>

                {showAdditionalFields && (
                    <>
                        <Grid item xs={12} md={4}>
                            <SelectMultiple
                                style={{ width: "100%" }}
                                width="100%"
                                name="status[]"
                                label={t("Categorie")}
                                options={anagrafiche?.categorie || []}
                                onChange={(event: any, newValue: any) => handleMultiSelectChange(event, newValue, 'categoryIds')}
                                selectedValues={categorySelectedIds}
                                placeholder={t("Categorie")} />
                        </Grid>

                        <Grid item xs={12} md={2.5}>
                            <DatePicker
                                label={t("Periodo prestazioni dal")}
                                value={processDateValue(date.itemStartDate)}
                                onChange={(date: Date | null) => {
                                    if (date) onDateChange("itemStartDate", date);
                                }}
                            />
                          
                        </Grid>

                        <Grid item xs={12} md={2.5}>
                              <DatePicker
                                label={t("Periodo prestazioni al")}
                                value={processDateValue(date.itemEndDate   )}
                                onChange={(date: Date | null) => {
                                    if (date) onDateChange("itemEndDate", date);
                                }}
                            />
                      
                        </Grid>

                        <Grid item xs={12} md={2.5}>
                            <Autocomplete
                                fullWidth
                                options={[
                                    { id: -1, nome: t("Tutti") },
                                    { id: 1, nome: t("Non assegnati") },
                                    ...(anagrafiche?.situazioneContabile || [])
                                ]}
                                getOptionLabel={(option) => option.nome}
                                value={[
                                    { id: -1, nome: t("Tutti") },
                                    { id: 1, nome: t("Non assegnati") },
                                    ...(anagrafiche?.situazioneContabile || [])
                                ].find((status: any) => status.id === customersFileQuery.situazioneContabile) || null}
                                onChange={(_, newValue) => {
                                    const event = {
                                        target: {
                                            name: "situazioneContabile",
                                            value: newValue ? newValue.id : -1
                                        }
                                    };
                                    handleInputChange(event);
                                }}
                                renderInput={(params) => (
                                    <TextField {...params} label={t("Situazione contabile")} />
                                )}
                            />
                        </Grid>

                        <Grid item xs={12} md={1.5}>
                            <Autocomplete
                                fullWidth
                                options={[
                                    { id: -1, nome: t("Tutte") },
                                    { id: 0, nome: t("Aperta") },
                                    { id: 1, nome: t("Chiusa") }
                                ]}
                                getOptionLabel={(option) => option.nome}
                                value={[
                                    { id: -1, nome: t("Tutte") },
                                    { id: 0, nome: t("Aperta") },
                                    { id: 1, nome: t("Chiusa") }
                                ].find((status: any) => status.id === customersFileQuery.dateType) || null}
                                onChange={(_, newValue) => {
                                    const event = {
                                        target: {
                                            name: "dateType",
                                            value: newValue ? newValue.id : -1
                                        }
                                    };
                                    handleInputChange(event);
                                }}
                                renderInput={(params) => (
                                    <TextField {...params} label={t("Data")} />
                                )}
                            />
                        </Grid>

                        <Grid item xs={12} md={2.5}>
                            <DatePicker
                                label={t("Dal")}
                                value={processDateValue(date.startDate)}
                                onChange={(date: Date | null) => {
                                    if (date) onDateChange("startDate", date);
                                }}
                            />
                            
                           
                        </Grid>

                        <Grid item xs={12} md={2.5}>
                            <DatePicker
                                label={t("Al")}
                                value={processDateValue(date.endDate)}
                                onChange={(date: Date | null) => {
                                    if (date) onDateChange("endDate", date);
                                }}
                            />
                            

                         
                        </Grid>

                        <Grid item xs={12} md={5} sx={{ mt: 4, ml: 1 }}>
                            <Box>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="withItemToFeeSearch"
                                            checked={customersFileQuery.withItemToFeeSearch || false}
                                            onChange={handleCheckboxChange}
                                        />
                                    }
                                    label={t("Da fatturare")}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="feeCustomer"
                                            checked={customersFileQuery.feeCustomer || false}
                                            onChange={handleCheckboxChange}
                                        />
                                    }
                                    label={t("Cliente fatturazione")}
                                />
                            </Box>
                        </Grid>
                    </>
                )}

                <Grid item xs={12} md={6} sx={{ display: "flex", justifyContent: "flex-start" }}>
                    <Button
                        style={cancelQueryButtonStyle}
                        onClick={toggleAdditionalFields}
                        startIcon={<Filter color="default" size="20px" />}
                    >
                        {showAdditionalFields ? t("Nascondi") : t("Mostra altri")}
                    </Button>
                </Grid>
                <Grid item xs={12} md={6} sx={{ display: "flex", justifyContent: "flex-end" }}>
                    <Button
                        style={cancelQueryButtonStyle}
                        onClick={handleReset}
                        color="error"
                    >
                        {t("Annulla ricerca")}
                    </Button>
                </Grid>
            </Grid>
        </Box>
    );
} 