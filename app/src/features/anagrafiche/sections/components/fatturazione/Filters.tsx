import { Box, Button, FormControl, InputLabel , Grid, Autocomplete, TextField, MenuItem} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomSelect } from "../../../../../custom-components/CustomSelect";
import { DatePicker } from "../../../../../components/ui-kit/DatePicker";
import { useState } from "react";
import moment from "moment";
import { processDateValue } from "../../../../../helpers/dateHelper";

const cancelQueryButtonStyle = {
    marginTop: "auto",
    marginLeft: "1rem",
};

export default function Filters(props: any) {
    const { query, setQuery, defaultQuery, lawyers,feeStatus } = props;
    const [date, setDate] = useState({
        startDateSearch: "",
        endDateSearch: "",
     });
    const { t } = useTranslation();

    const onChangeFilterInputs = (e: any) => {
        setQuery((items: any) => ({
            ...items,
            [e.target.name]: e.target.value,
        }));
    };
    const onDateChange = (name: string, value: Date) => {
        setDate((prevValue: any) => ({ ...prevValue, [name]: moment(value).format("DD/MM/YYYY") }));
        setQuery((prevQuery: any) => ({...prevQuery,[name]: moment(value).format("DD/MM/YYYY")}))
      
    };
    const handleReset = () => {
        setDate({
            startDateSearch: "",
            endDateSearch: "",
        });
        setQuery({ ...defaultQuery,   tipologia: "preavvisiDiParcella",peopleId: query.peopleId });
    };

    return (
        <>
        <Box component="form" display="flex" alignItems="end" gap={1}>
                <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >
                    <Autocomplete
                        fullWidth
                        options={[
                            { value: "preavvisiDiParcella", label: t("Preavvisi di parcella") },
                            { value: "fatture", label: t("Fatture") },
                            { value: "fattureElettroniche", label: t("Fatture elettroniche") },
                            { value: "noteDiCreditoElettroniche", label: t("Note di credito elettroniche") }
                        ]}
                        getOptionLabel={(option) => option.label}
                        value={[
                            { value: "preavvisiDiParcella", label: t("Preavvisi di parcella") },
                            { value: "fatture", label: t("Fatture") },
                            { value: "fattureElettroniche", label: t("Fatture elettroniche") },
                            { value: "noteDiCreditoElettroniche", label: t("Note di credito elettroniche") }
                        ].find((option) => option.value === query.tipologia) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "tipologia",
                                    value: newValue ? newValue.value : "preavvisiDiParcella"
                                }
                            };
                            onChangeFilterInputs(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Tipologia")} />
                        )}
                    />
                    </Grid>
                    <Grid
                        item
                        xs={12}
                            sm={3}
                            md={3}
                        
                    >
                    <Autocomplete
                        fullWidth
                        disabled={query.tipologia !== "preavvisiDiParcella"}
                        options={[
                            { id: -1, nome: "-" },
                            ...(feeStatus || [])
                        ]}
                        getOptionLabel={(option) => option.nome}
                        value={[
                            { id: -1, nome: "-" },
                            ...(feeStatus || [])
                        ].find((fee: any) => fee.id === query.idFeeNoticeStatusSearch) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "idFeeNoticeStatusSearch",
                                    value: newValue ? newValue.id : -1
                                }
                            };
                            onChangeFilterInputs(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Situazione preavviso")} />
                        )}
                    />

                    </Grid>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}     
                    >
                        <DatePicker
                            label={t("Periodo dal")}
                            value={processDateValue(date.startDateSearch)}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("startDateSearch", date);
                            }}
                        />
                    </Grid>
                      <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >    
                        <DatePicker
                            label={t("Periodo al")}
                            value={processDateValue(date.endDateSearch)}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("endDateSearch", date);
                            }}
                            minDate={date.startDateSearch ? new Date(date.startDateSearch) : undefined}
                        
                /> </Grid>
                </Grid>
                 
        </Box>
              <Box component="form" display="flex" alignItems="end" gap={1} sx={{pt: 2}}>
                 <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >
                        <Autocomplete
                            fullWidth
                            options={[
                                { id: -1, nome: t("Tutti gli stati") },
                                { id: 1, nome: t("Da incassare") },
                                { id: 2, nome: t("Incassate") }
                            ]}
                            getOptionLabel={(option) => option.nome}
                            value={[
                                { id: -1, nome: t("Tutti gli stati") },
                                { id: 1, nome: t("Da incassare") },
                                { id: 2, nome: t("Incassate") }
                            ].find((status: any) => status.id === query.statusSearch) || null}
                            onChange={(_, newValue) => {
                                const event = {
                                    target: {
                                        name: "statusSearch",
                                        value: newValue ? newValue.id : -1
                                    }
                                };
                                onChangeFilterInputs(event);
                            }}
                            renderInput={(params) => (
                                <TextField {...params} label={t("Stato")} />
                            )}
                        />
                    </Grid>
                     <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    > 
                    <FormControl variant="outlined" fullWidth>
                    <InputLabel id='select-label'>{t("Emittente")}</InputLabel>
                    <CustomSelect       
                        value={query.emittenteSearch}
                        onChange={onChangeFilterInputs}
                        name='emittenteSearch'
                        group='attivo'
                        dataSource={lawyers}
                        valueKey='id'
                        
                    >
                        <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>
                        {lawyers?.map((rec: any) => (
                            <MenuItem key={rec?.id} value={rec?.id}>{rec?.nome}</MenuItem>
                        ))}
                    
                    </CustomSelect>
                </FormControl></Grid>
                 <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    > 
                      <Autocomplete
                          fullWidth
                          options={[
                              { id: 0, nome: t("Tutti") },
                              { id: "on", nome: t("Solo acconti") }
                          ]}
                          getOptionLabel={(option) => option.nome}
                          value={[
                              { id: 0, nome: t("Tutti") },
                              { id: "on", nome: t("Solo acconti") }
                          ].find((advance: any) => advance.id === query.advanceSearch) || null}
                          onChange={(_, newValue) => {
                              const event = {
                                  target: {
                                      name: "advanceSearch",
                                      value: newValue ? newValue.id : 0
                                  }
                              };
                              onChangeFilterInputs(event);
                          }}
                          renderInput={(params) => (
                              <TextField {...params} label={t("Tipo acconti")} />
                          )}
                      />
            </Grid>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        style={{display: "flex", justifyContent: "flex-start" }}
                        
                    >
                    <Button  style={cancelQueryButtonStyle} onClick={handleReset}>
            {t("Annulla ricerca")}
            </Button>
            </Grid>
                </Grid>

        </Box>
        </>
    );
}
