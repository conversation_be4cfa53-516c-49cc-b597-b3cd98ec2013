import {
    Box,
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    TextField,
    Autocomplete,
} from "@vapor/react-material";
import React from "react";
import { Tab, Tabs } from "@vapor/react-extended";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { a11yProps, CustomTabPanel } from "../../../helpers/customTabPanel";
import { RELATIONS_OPTIONS } from "../constants/constants";

export const AnagraficheTabs = (props: any) => {
    const { t } = useTranslation();
    const [value, setValue] = React.useState(0);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const {
        params: defaultParams,
        onChangeFunctions,
        categorie,
        assigneeList,
    } = props;

    const {
        onChangeCheckbox,
        onChangeInput,
        onClickReset,
        onSubmit,
        onDateChange,
    } = onChangeFunctions;

    return (
        <>
            <Box>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label="Ricerca" {...a11yProps(0)} />
                    <Tab label="Ricerca per indirizi" {...a11yProps(1)} />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
                <Box display="flex" alignItems="end" gap={2}>
                    <TextField
                        label="Ricerca:"
                        variant="outlined"
                        value={defaultParams.searchField}
                        placeholder="Ricerca"
                        name="searchField"
                        sx={{ width: 1 / 4 }}
                        onChange={onChangeInput}
                    />
                    <Autocomplete
                        sx={{ width: 1 / 4 }}
                        options={RELATIONS_OPTIONS}
                        getOptionLabel={(option) => option.label}
                        value={RELATIONS_OPTIONS.find((option: any) => option.value === defaultParams.searchRelation) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "searchRelation",
                                    value: newValue ? newValue.value : "-1"
                                }
                            };
                            onChangeInput(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Tutte le Relazioni")} />
                        )}
                    />
                    <Autocomplete
                        sx={{ width: 1 / 4 }}
                        options={assigneeList || []}
                        getOptionLabel={(option) => option.nomeutente}
                        value={assigneeList?.find((category: any) => category.id === defaultParams.searchAssociazione) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "searchAssociazione",
                                    value: newValue ? newValue.id : -1
                                }
                            };
                            onChangeInput(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Qualsiasi associazione")} />
                        )}
                    />
                    <TextField
                        label={t("Tags:")}
                        variant="outlined"
                        value={defaultParams.searchTags}
                        placeholder="Tags"
                        name="searchTags"
                        sx={{ width: 1 / 4 }}
                        onChange={onChangeInput}
                    />
                </Box>
                <Box
                    display="flex"
                    alignItems="end"
                    gap={2}
                    style={{ marginTop: "7px" }}
                >
                    <FormControlLabel
                        sx={{ width: 1 / 7 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="searchTagsLike"
                                checked={!!defaultParams.searchTagsLike}
                            />
                        }
                        label={t("Cerca per 'contiene'")}
                    />
                    <div style={{ width: "25%" }}>
                        <DatePicker
                            label={t("Start Date")}
                            value={defaultParams.startAnagraficheSearchValue}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("startAnagraficheSearchValue", date);
                            }}
                        />
                    </div>
                    <div style={{ width: "25%" }}>
                        <DatePicker
                            label="End Date"
                            value={defaultParams.endAnagraficheSearchValue}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("endAnagraficheSearchValue", date);
                            }}
                        />
                    </div>
                    <Autocomplete
                        sx={{ width: 1 / 5 }}
                        options={categorie || []}
                        getOptionLabel={(option) => option.nome}
                        value={categorie?.find((category: any) => category.id === defaultParams.categoryId) || null}
                        onChange={(_, newValue) => {
                            const event = {
                                target: {
                                    name: "categoryId",
                                    value: newValue ? newValue.id : -1
                                }
                            };
                            onChangeInput(event);
                        }}
                        renderInput={(params) => (
                            <TextField {...params} label={t("Tutte le categorie")} />
                        )}
                    />
                    <FormControlLabel
                        sx={{ width: 1 / 7 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="showAdvanceDynamic"
                                checked={!!defaultParams.showAdvanceDynamic}
                            />
                        }
                        label={t("Mostra Campi")}
                    />
                    <Button
                        variant="contained"
                        sx={{ width: 1 / 10 }}
                        color="primary"
                        type="submit"
                        onClick={onSubmit}
                    >
                        {t("Cerca")}
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        sx={{ width: 1 / 10 }}
                        onClick={onClickReset}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Box display="flex" alignItems="end" gap={2}>
                    <TextField
                        label={t("Nazione:")}
                        variant="outlined"
                        value={defaultParams.searchNation}
                        placeholder={t("Cerca nazione")}
                        name="searchNation"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />
                    <TextField
                        label={t("Città:")}
                        variant="outlined"
                        value={defaultParams.searchCity}
                        placeholder={t("Cerca città")}
                        name="searchCity"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Via:")}
                        variant="outlined"
                        value={defaultParams.searchWay}
                        placeholder={t("Cerca via")}
                        name="searchWay"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Cap:")}
                        variant="outlined"
                        value={defaultParams.searchCap}
                        placeholder={t("Cerca cap")}
                        name="searchCap"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Provincia:")}
                        variant="outlined"
                        value={defaultParams.searchProvince}
                        placeholder={t("Cerca provincia")}
                        name="searchProvince"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Regione:")}
                        variant="outlined"
                        value={defaultParams.searchRegion}
                        placeholder={t("Cerca region")}
                        name="searchRegion"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />
                    <Button
                        variant="contained"
                        color="primary"
                        type="submit"
                        onClick={onSubmit}
                    >
                        {t("Cerca")}
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        onClick={onClickReset}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
            </CustomTabPanel>
        </>
    );
};
