# OneFront - React Boilerplate

This boilerplate is a foundational React application template designed for use on a single platform, serving as a starting point to build various types of web applications tailored to the company's needs.

To learn more about OneFront, visit: [OneFront Docs](https://development.teamsystem.com/catalog/default/component/one-front/docs/).

## Table of Contents
- [OneFront - React Boilerplate](#onefront---react-boilerplate)
  - [Table of Contents](#table-of-contents)
  - [Quick Start](#quick-start)
  - [Project Architecture](#project-architecture)
    - [Tools](#tools)
    - [Host Ports](#host-ports)
    - [Structure](#structure)
  - [Services](#services)
    - [Registry Service](#registry-service)
    - [Proxy Service](#proxy-service)
    - [Meta Information](#meta-information)
  - [Useful links](#useful-links)

## Quick Start

1. Make sure you have `docker`, `docker-compose` and *optionally* `node@lts` installed on your system
2. Clone this repository if you haven't already
3. Setup your docker environment for pulling from the company Nexus: [Docker Guide](https://development.teamsystem.com/docs/default/component/one-front/ENVIRONMENT-SETUP/#docker-login)
4. Setup a global `.npmrc`: [NPM Guide](https://development.teamsystem.com/docs/default/component/one-front/ENVIRONMENT-SETUP/#npmrc)
5. Setup your `.env` file starting from `.env.example`: [Environment Guide](https://development.teamsystem.com/docs/default/component/one-front/ENVIRONMENT-SETUP/#env)
6. Open a Linux/WSL terminal and run `make start`
7. Open your browser at [http://localhost:3000](http://localhost:3000) to use the app

Once you're done working on the project, run `make stop` to gracefully terminate it

> 🚧 **Windows Users** 🚧
>
> Docker + React are going through some rough time on Windows and Hot Module Reload doesn't perform well.
>
> When running Node outside of Docker, please use the latest LTS version of Node found [here](https://nodejs.org/en) or by using your Node version manager.
> 
> `Optional`: you can also install [Docker Desktop](https://docs.docker.com/desktop/install/windows-install/) (integrated with WSL2) to run the process in the background and have a GUI for the containers.
>
> Please use:
>
> - `make start-api` to run the backend services on Docker
> - `make start-app` to run the frontend on your local NodeJS

## Project Architecture

- Docker is used to isolate and deploy the services involved
- Makefile is used to automate lifecycle and development tasks

### Tools

- [Docker & Docker-Compose](https://docs.docker.com/compose/)
- [Create React App](https://create-react-app.dev/)
- [Makefile](https://biosphere.teamsystem.com/oneplatform/onefront/adrs/-/tree/main/makefile-project-api)

### Host Ports

- Services
  - `4010`: Proxy Service
  - `4020`: Registry Service
- Apps
  - `3000`: React App

### Structure
- **/app** - contains the source code of your app, based on Create React App and implements the OneFront SDK.
- **/local-mocks** - contains code which lets users work indipendently without a backend or external services involved.
- **/meta** - contains customizable seed information such as i18n and registries, visit the [section](#meta-information).

## Services

### Registry Service

> Reference: [Registry Service Developer Portal](https://development.teamsystem.com/catalog/default/component/one-front/docs/registry-service/)

The registry service provides OnePlatform registry-service emulation, it makes use of a mechanic which lets you refer to any service by its logical ID (`URN`) in your source code:

```js
// Example of calling a service by its URN
const { loading, data } = useGet("serviceId://internal/uri");
```

### Proxy Service

> Reference: [Proxy Service Developer Portal](https://development.teamsystem.com/catalog/default/component/one-front/docs/proxy-service/)

The service which proxies requests from the SPA (app) to any backend service known by the service registry.

### Meta Information

Different services may need some external configuration to work, and you can use [service-meta](https://www.npmjs.com/package/@forrestjs/service-meta) to easily import such information into the project.

Each configuration file will be sourced in the following order:

- `{file_name}.{env_name}.json`
- `{file_name}.local.json`
- `{file_name}.json`

> **NOTE:** environment and local suffixed files are gitignored.

## Useful links

- How to use local mocks: [Guide](https://development.teamsystem.com/catalog/default/component/one-front/docs/tutorials/proxy-service/mock-js)
- CORS and Proxy Sidecar ADR: [Info](https://biosphere.teamsystem.com/oneplatform/onefront/adrs/-/tree/main/cors-and-proxy-sidecar)