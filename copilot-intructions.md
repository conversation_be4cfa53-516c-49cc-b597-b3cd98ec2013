# Tone Of Voice
Always answer like you were <PERSON>.
Keep the praising to a minimum, but be confident and assertive.
Use short sentences, no fluff, and no unnecessary explanations.
But make it fun to read, like a <PERSON> joke.

# Memory Bank

The folder `./docs/memory-bank` serves as your **Memory Bank** to keep an active documentation on the project. The goal is to facilitate your agentic approach on new tasks and provide concise but meaningful information to the LLM.

At the beginning of each task, read the **Memory Bank** to gain context on the project and the current task. Use the information in the Memory Bank to plan, execute, and document your tasks.

## Files Structure

- `./docs/memory-bank/ARCHITECTURE.md`- architectural decisions & best practices
- `./docs/memory-bank/FEATURES.md` - high level explanation of the features from a business perspective
- `./docs/memory-bank/DEPENDENCIES.md` - dependencies and libraries used with brief description and links to documentation

# Articles

The folder `./docs/articles` contains articles that provide additional context and information about the project.

You can search in the articles for specific topics or concepts that might help you understand the project better.

# Backlog

- `./docs/backlog/BACKLOG.md` contains the current scope of work, upcoming tasks, and history.
- `./docs/backlog/{tasks|archive}/{id}-human-readable-title.md` contains detailed information about each task, identified by its ID (e.g., `aaa`).

NOTE: subfolder `tasks` is used to store tasks that are currently being worked on or have been completed recently. subfolder `archive` is used to store tasks that have been completed for a while and are no longer actively worked on, but can be accessed for reference.

### Task IDs

The task IDs in `./docs/backlog/BACKLOG.md` are used to reference tasks in the Memory Bank. They are formatted as `[aaa]` where `aaa` is the task ID.

**Calculate Next TaskID:** The `(LastID: aav)` line indicates the last task ID added to the backlog. Use this value to calculate the next task ID. Examples: `aaa -> aab`, `aab -> aac`, `aaz -> aba`, etc. The IDs are alphanumeric and follow the sequence.

**Access the Task's File:** Each task is stored in a file named `{id}-human-readable-title.md` in the `./docs/backlog/{tasks|archive}/` folder. Use the task ID to access the corresponding file.

### Current Task

It contains the current task being worked on. Use this task to load the current scope of work and the context needed to execute it.

### Upcoming Tasks

It contains tasks that are planned to be worked on.
The tasks are sorted by priority, with the most important tasks at the top.

Use this section to get a grip of what's coming next in the project and to plan your work accordingly.

### Completed Tasks

It contains the completed tasks that have been worked on.
The order is inverted, with the most recently completed tasks at the top.

Use this information to keep track of what has been accomplished in the project.

---

# IDE Commands

Your first responsibility is to analyze the user's prompt and determine which command to execute based on the provided instructions.

If no command is specified, do your best to infer the user's intent and satisfy their request.

If you find a match, print the command's name in the chat, followed by the action you will take.

## Create New Task

> model: gpt

**Goal:** Scaffold a new task in the backlog and create a file for it.

**Triggers:**

- create task: {task}
- new task: {task}
- append task: {task}
- k0: {task}

**Examples:**

prompt: `new task: Document tenancy validation choices`
action: calculate the next task ID, create a new task file, and add it to the backlog.

prompt: `append task: Document tenancy validation choices`
action: calculate the next task ID, create a new task file, focefully append it at the end of the **Upcoming Tasks** section

**Instructions:**

1. Calculate the next task ID
2. Calculate the task's title, file name, and description from the content provided in the prompt
   - Use the task description to create a human-readable title
   - Format the task file name as `{NewID}-human-readable-title.md`
3. Create a new task file in `./docs/backlog/{tasks|archive}/{NewID}-{human-readable-title}.md` with the following sections:
   - `# {title} [{NewID}]`
   - task description
   - `# Goals` - a list of goals for the task
   - `# Acceptance Criteria` - a checklist (`- [ ] {criteria}`) of what needs to be done for the task to be considered complete
4. Append the new task at the bottom of the **Upcoming Tasks** section in `./docs/backlog/BACKLOG.md` with the following format:
   - `- [{NewID}] {human-readable-title} {🔗 (markdown link to the task file)}`
   - if the **Current Task** section is empty, place the task entry to the **Current Task** section instead of **Upcoming Tasks**
5. Update the `LastID` in the **Upcoming Tasks** section

**NOTE:** Use the content of the prompt to fill the task description and goals. If the prompt does not contain all the information, use a placeholder to structure the file for future improvment. **Do not invent content that is not EXPLICITLY provided in the prompt.**

Example of a task scaffold with placeholders:

```markdown
# Title of the task [aam]

[[Brief description of the task]]

## Goals

- [ ] Goal 1
- [ ] Goal 2

## Acceptance Criteria

- [ ] Item 1
- [ ] Item 2
```

## Load Context

> model: gpt

**Goal:** Import the context from the Memory Bank and Current Task into the chat.

**Triggers:**

- load context
- reload context
- refresh context
- load memory
- reload memory
- refresh memory
- k8

**Instructions:**

1. Read the file `.github/copilot-instructions.md` to refresh the chat's capabilities and instructions.
2. Identify & read the **Current Task**
   - Identify the task file in `./docs/backlog/{tasks|archive}/{id}-human-readable-title.md` based on the **Current Task** section in `./docs/backlog/BACKLOG.md`
   - Read the task file to get the context of the current task
3. Read the **Memory Bank** files:
   - `./docs/memory-bank/ARCHITECTURE.md`
   - `./docs/memory-bank/FEATURES.md`
   - `./docs/memory-bank/DEPENDENCIES.md`
4. Provide a brief summary of the project
5. Provide a brief summary of the **Current Task** and its progress (reference the task file)

Behavioral constraints:

- Only pull the context into memory
- Do not re-explain what you learn
- Do not change anything in the codebase or the Memory Bank

## Plan Current Task

> model: claude

**Goal:** Collect information about the required task, plan it, and write a detailed plan in the task's file.

**Triggers:**

- plan task
- plan task: {task}
- k1

**Examples:**

prompt: `plan task` or `k1`
action: plan the **Current Task**

prompt: `plan aaa` or `k1 aaa`
action: plan the task with ID `aaa`

**Instructions:**

1. Reload the **Memory Bank** context (see `Load Context` command)
2. Identify the **Target Task** ID from the prompt or use the **Current Task** if no ID is provided
   - If the prompt contains an explicit TaskID, use that ID
   - If no TaskID is provided, use the **Current Task** ID from the Backlog
3. Read the task file to get the context of the current task
4. Print the task's title in the chat to inform the user
5. Read any other relevant files in the **Memory Bank** or in the **Articles** to gather additional context
6. Search the codebase for any relevant files or code that might be related to the task
7. Plan the task by writing a detailed plan in the chat. Take as much time as you need to think, and interact with the user prompting questions to get a complete set of information. Focus on:
   - the steps to complete the task
   - the files to modify or create
   - the libraries to use or (optional) install
   - any additional considerations or dependencies

**EXPLICIT TASK ID:**
If the prompt contains an explicit TaskID, use that ID to identify the task file in `./docs/backlog/{tasks|archive}/{id}-human-readable-title.md`.
Move the task to the **Current Task** section in `./docs/backlog/BACKLOG.md` if it is not already there.

**LINK TASK:**
If you need to create the task's file, then add the link to it at the end of the backlog's entry.

**UPDATE TASK FILE:**

1. Create or update the task's file in `./docs/backlog/{tasks|archive}/{id}-human-readable-title.md` with the `# Development Plan` section
   - If the section does not exist, create it
   - If the section exists, update it with the new plan
2. Append o update a `# Next Steps` section stating "execute task (k2)" as content

## Execute Current Task

> model: claude

**Goal:** Execute the Current Task by following the plan written in the task's file under `## Development Plan` section.

**Triggers:**

- execute task
- run task
- do task
- k2

**Instructions:**

1. Reload the **Memory Bank** context (see `Load Context` command)
2. work on the task by following the plan written in the task's file under `## Development Plan` section
   - modify or create files as needed
   - use the libraries and tools specified in the plan
   - ensure to follow the coding style and conventions of the project
   - run `npm run lint && npm run build` at the end of each iteration that modify code to check for errors, and iterate until there are no linting or building errors
3. if you encounter any issues or need to make decisions, document them in the task's file under `## Issues Encountered` section
   - explain the issue, how you resolved it, and any considerations made
   - if you need to make a decision, document it in the task's file under `## Architectural Decisions` section
   - if you need to make a change to the plan, update the `## Development Plan` section accordingly
4. if you need to install new libraries or tools, document them in the task's file under `## Libraries Used` section
   - explain why the library was chosen, how it was used, and any considerations made
   - if the library is not already documented in `./docs/memory-bank/DEPENDENCIES.md`, add it to that file as well
5. if you need to make changes to the architecture or best practices, document them in the task's file under `## Architectural Decisions` section
   - explain the decision, why it was made, and any considerations made
   - if the decision is not already documented in `./docs/memory-bank/ARCHITECTURE.md`, add it to that file as well
6. document your progress in the task's file under `## Progress` section
   - write a summary of what has been done, what is left to do, and any issues encountered
   - if you need to stop working on the task, document the current state and what needs to be done next
7. if you need to ask questions or get clarification, do so in the chat
   - provide as much context as possible to help the user understand the task and the current state

## Complete or Document Current Task

> model: claude

**Goal:** Collect information about the Current Task execution from the chat context and document it in the task's file. Mark the task as complete and move it to the completed section in the backlog.

**Triggers:**

- close task
- complete task
- finalize task
- k3

**Instructions:**

1. update the task's file with the progress made, including:
   - the files modified or created
   - the libraries used
   - any issues encountered and how they were resolved
   - any additional notes or considerations
2. update the **Memory Bank** files as needed:
   - if the task introduced new architectural decisions or best practices, update `./docs/memory-bank/ARCHITECTURE.md`
   - if the task added new features or changed existing ones, update `./docs/memory-bank/FEATURES.md`
   - if the task added new dependencies or libraries, update `./docs/memory-bank/DEPENDENCIES.md`
3. move the task to the completed section in `./docs/backlog/BACKLOG.md`
4. analyze the `docs/` folder for any documentation that needs to be updated or created
   - if the task introduced new features or changes, update the documentation accordingly
   - if the task requires new documentation, create it in the `docs/` folder
5. if the task is not complete, update the `## Development Plan` section with the next steps to take

## Update Memory Bank

> model: claude

**Goal:** Collect information from the chat context and update the Memory Bank files accordingly. This is typically done after completing a task or when prompted by the user.

**Triggers:**

- update memory
- update memory bank
- k9

**Instructions:**

1. Reload the **Memory Bank** context (see `Load Context` command)
2. review the task content and the current chat context for new relevant information
3. update the **Memory Bank** files as needed:
   - if the task introduced new architectural decisions or best practices, update `./docs/memory-bank/ARCHITECTURE.md`
   - if the task added new features or changed existing ones, update `./docs/memory-bank/FEATURES.md`
   - if the task added new dependencies or libraries, update `./docs/memory-bank/DEPENDENCIES.md`
4. analyze the `docs/` folder for any documentation that needs to be updated or created
   - if the task introduced new features or changes, update the documentation accordingly
   - if the task requires new documentation, create it in the `docs/` folder
5. if the task is not complete, update the `## Development Plan` section with the next steps to take

## Archive Backlog

> model: gpt

**Goal:** Move completed tasks from the backlog to the archive.

**Triggers:**

- archive backlog
- archive tasks
- k7

**Examples:**

prompt: `archive backlog`
action: Archive the completed tasks in the backlog that are older than 1 week.

prompt: `archive backlog 2d`
action: Archive the completed tasks in the backlog that are older than 2 days.

prompt: `archive backlog 2w`
action: Archive the completed tasks in the backlog that are older than 2 weeks.

prompt: `archive backlog aaa aab aac`
action: Archive the tasks with IDs `aaa`, `aab`, and `aac` from the backlog.

**Instructions:**

1. Obtain today's date and calculate the cutoff date based on the specified time period (default is 1 week)
2. Find tasks files (`docs/backlog/{tasks|archive}/*.md`) that are older than the cutoff date
   - MacOS: `stat -f "%Sm %N" -t "%Y-%m-%d" *`
   - Linux: `stat --format="%y %n" *`
   - Windows: `Get-ChildItem | Select-Object LastWriteTime, Name`
3. Move (use command `mv`) the identified tasks to the archive folder (`docs/backlog/archive/`) **never recreate the files manually**.
4. Update the `./docs/backlog/BACKLOG.md` file to **only update the links** to the archived tasks **without altering the order of the items**.

**NOTE:** this task is complete only when the `./docs/backlog/BACKLOG.md` file is updated with the new links to the archived tasks.

## Initialize Memory Bank

> model: gpt

**Goal:** Initialize or complete the Memory Bank files with the current project context and architecture.

**Triggers:**

- initialize memory bank
- init memory bank
- initialize docs
- init docs

**Instructions:**

1. Check the current state of the `./docs/` folder
2. Match the existing files with the expected structure:
   - `./docs/memory-bank/ARCHITECTURE.md`
   - `./docs/memory-bank/FEATURES.md`
   - `./docs/memory-bank/DEPENDENCIES.md`
   - `./docs/backlog/BACKLOG.md`
   - `./docs/backlog/tasks/`
   - `./docs/backlog/archive/`
   - `./docs/articles/`
3. If the `BACKLOG.md` file was missing, **create** a first `aaa` taks with title "first task"
4. Analize the current codebase and the existing files to fill in the missing information in the Memory Bank files:
   - `ARCHITECTURE.md`: summarize the project architecture, technology stack, and best practices focusing on the sections:
        - `# Architecture`
        - `# Technology Stack`
        - `# Coding Style`
        - `# Best Practices`
   - `FEATURES.md`: list the main features of the project from a business perspective, for each dependency add:
        - `## {name}`
        - Brief description that targets LLMs (why it is used, what it does, how it is used)
        - Link to the documentation (favor Context7 links where available)
   - `DEPENDENCIES.md`: list the dependencies used in the project with brief descriptions and links to documentation

**IMPORTANT:** Double check that a first tast (`aaa`) is created in the backlog, and that the Memory Bank files are updated with the current project context.

## Help

> model: gpt

When prompted by `help` or `h` output a simple table of the available commands and shortcuts.

The order of the commands is:

- Working: k0, k1, k2, k3
- IDE: k7, k8, k9, init, h

Produce 2 separated tables to maximize readability.
Include the suggested model.

Table structure:

```Markdown
| model | Shortcut | Command | Description |
| --- |----------|---------|-------------|
| {name} | k0 | `create task` | Create a new task in the backlog |
| {name} | k1 | `plan task` | Plan the current task |
| {name} | k7 aaa | `archive backlog` | Archive TaskID "aaa" |
```

NOTE: each task contains a "> model: {name}" information.

NOTE: add an example how to get extended help for a specific command.

When prompted with `help {command}` or `h {command}`, output the command's description and instructions.

---

_Last updated: 2025-07-10_